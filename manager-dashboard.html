<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ePTW System - Manager Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body class="dashboard-page manager-dashboard">
    <!-- Navigation Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="header-left">
                <div class="logo-section">
                    <span class="logo-icon">🛡️</span>
                    <h1>ePTW System</h1>
                </div>
                <nav class="main-nav">
                    <button class="nav-button active" data-section="overview">
                        <span class="nav-icon">📊</span>
                        Overview
                    </button>
                    <button class="nav-button" data-section="permits">
                        <span class="nav-icon">📋</span>
                        Permits
                    </button>
                    <button class="nav-button" data-section="history">
                        <span class="nav-icon">📚</span>
                        History
                    </button>
                </nav>
            </div>
            <div class="header-right">
                <div class="department-badge" id="departmentBadge">
                    <span class="dept-icon" id="deptIcon">⚡</span>
                    <span class="dept-name" id="deptName">Electrical</span>
                </div>
                <div class="user-info">
                    <span class="user-role" id="userRoleIcon">👨‍💼</span>
                    <div class="user-details">
                        <span class="user-name" id="userName">Manager</span>
                        <span class="user-role-text" id="userRoleText">Department Manager</span>
                    </div>
                </div>
                <button class="refresh-button" id="refreshButton" title="Refresh Data">
                    <span class="refresh-icon">🔄</span>
                </button>
                <button class="logout-button" id="logoutButton" title="Logout">
                    <span class="logout-icon">🚪</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="dashboard-main">
        <!-- Overview Section -->
        <section id="overviewSection" class="dashboard-section active">
            <div class="section-header">
                <h2>Department Overview</h2>
                <p class="section-subtitle" id="overviewSubtitle">Monitor permits for your department</p>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card pending">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <h3 id="pendingCount">0</h3>
                        <p>Pending Approval</p>
                    </div>
                    <div class="stat-action">
                        <button class="stat-button" id="viewPendingBtn">Review</button>
                    </div>
                </div>
                <div class="stat-card approved">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 id="approvedCount">0</h3>
                        <p>Approved Today</p>
                    </div>
                </div>
                <div class="stat-card rejected">
                    <div class="stat-icon">❌</div>
                    <div class="stat-content">
                        <h3 id="rejectedCount">0</h3>
                        <p>Rejected Today</p>
                    </div>
                </div>
                <div class="stat-card total">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="totalCount">0</h3>
                        <p>Total This Month</p>
                    </div>
                </div>
            </div>

            <!-- Priority Permits -->
            <div class="priority-section">
                <h3>Permits Requiring Attention</h3>
                <div id="priorityPermits" class="priority-list">
                    <!-- Priority permits will be populated by JavaScript -->
                </div>
            </div>

            <!-- Recent Decisions -->
            <div class="recent-decisions">
                <h3>Recent Decisions</h3>
                <div id="recentDecisions" class="decisions-list">
                    <!-- Recent decisions will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Permits Section -->
        <section id="permitsSection" class="dashboard-section">
            <div class="section-header">
                <h2>Department Permits</h2>
                <div class="section-actions">
                    <div class="search-box">
                        <input type="text" id="permitSearch" placeholder="Search permits..." class="search-input">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                        <option value="Closed">Closed</option>
                    </select>
                    <select id="dateFilter" class="filter-select">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>
            </div>

            <!-- Permits Grid -->
            <div id="permitsGrid" class="permits-grid">
                <!-- Permit cards will be populated by JavaScript -->
            </div>
        </section>

        <!-- History Section -->
        <section id="historySection" class="dashboard-section">
            <div class="section-header">
                <h2>Decision History</h2>
                <div class="section-actions">
                    <div class="search-box">
                        <input type="text" id="historySearch" placeholder="Search history..." class="search-input">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="historyFilter" class="filter-select">
                        <option value="">All Decisions</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>
            </div>

            <!-- History List -->
            <div id="historyList" class="history-list">
                <!-- History items will be populated by JavaScript -->
            </div>
        </section>
    </main>

    <!-- Permit Detail Modal -->
    <div id="permitDetailModal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Permit Details</h3>
                <button class="modal-close" id="closeDetailModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="permit-detail-content">
                    <!-- Permit details will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <div class="decision-actions">
                    <button class="reject-button" id="rejectPermitBtn">
                        <span class="button-icon">❌</span>
                        Reject
                    </button>
                    <button class="approve-button" id="approvePermitBtn">
                        <span class="button-icon">✅</span>
                        Approve
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Reason Modal -->
    <div id="rejectionModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Rejection Reason</h3>
                <button class="modal-close" id="closeRejectionModal">&times;</button>
            </div>
            <form id="rejectionForm" class="modal-form">
                <div class="form-group">
                    <label for="rejectionReason">Reason for Rejection *</label>
                    <textarea id="rejectionReason" name="rejectionReason" required class="form-textarea" rows="4" placeholder="Please provide a detailed reason for rejecting this permit..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="secondary-button" id="cancelRejection">Cancel</button>
                    <button type="submit" class="reject-button" id="confirmRejection">
                        <span class="button-text">Confirm Rejection</span>
                        <span class="button-spinner hidden">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Approval Confirmation Modal -->
    <div id="approvalModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Approve Permit</h3>
                <button class="modal-close" id="closeApprovalModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="approval-content">
                    <div class="approval-icon">✅</div>
                    <h4>Confirm Permit Approval</h4>
                    <p>Are you sure you want to approve this permit? This action cannot be undone.</p>
                    <div class="permit-summary" id="approvalPermitSummary">
                        <!-- Permit summary will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="secondary-button" id="cancelApproval">Cancel</button>
                <button class="approve-button" id="confirmApproval">
                    <span class="button-text">Approve Permit</span>
                    <span class="button-spinner hidden">
                        <div class="spinner"></div>
                    </span>
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="script.js"></script>
    <script>
        // Initialize manager dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeManagerDashboard();
        });
    </script>
</body>
</html>
