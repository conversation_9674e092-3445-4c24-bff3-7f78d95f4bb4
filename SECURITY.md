# ePTW System Security Guide

This document outlines security best practices and guidelines for the electronic Permit To Work (ePTW) system deployment and operation.

## Security Overview

The ePTW system handles sensitive industrial safety data and requires robust security measures to protect against unauthorized access, data breaches, and system compromise.

### Security Principles
1. **Defense in Depth**: Multiple layers of security controls
2. **Least Privilege**: Users have minimum necessary access
3. **Data Protection**: Encryption in transit and at rest
4. **Audit Trail**: Complete logging of all activities
5. **Regular Updates**: Timely security patches and updates

## Authentication and Authorization

### 1. User Authentication
**Password Requirements**:
- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, symbols
- No dictionary words or personal information
- Regular password changes (90 days)

**Implementation**:
```javascript
// Strong password validation
function validatePassword(password) {
    const requirements = [
        { regex: /.{12,}/, message: "At least 12 characters" },
        { regex: /[A-Z]/, message: "At least one uppercase letter" },
        { regex: /[a-z]/, message: "At least one lowercase letter" },
        { regex: /\d/, message: "At least one number" },
        { regex: /[!@#$%^&*]/, message: "At least one special character" }
    ];
    
    return requirements.filter(req => !req.regex.test(password));
}
```

### 2. Session Management
**Security Settings**:
```javascript
const SECURITY_CONFIG = {
    SESSION_TIMEOUT: 4 * 60 * 60 * 1000, // 4 hours max
    IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes idle
    MAX_LOGIN_ATTEMPTS: 3,
    LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
    SECURE_COOKIES: true,
    SAME_SITE: 'strict'
};
```

### 3. Role-Based Access Control (RBAC)
**Access Matrix**:
| Role | Create Permits | Approve Permits | Conduct Audits | View All Depts |
|------|----------------|-----------------|----------------|----------------|
| Fireman | ✅ | ❌ | ✅ | ✅ |
| Electrical Manager | ❌ | ✅ (Electrical) | ❌ | ❌ |
| Mechanical Manager | ❌ | ✅ (Mechanical) | ❌ | ❌ |
| Civil Manager | ❌ | ✅ (Civil) | ❌ | ❌ |

## Network Security

### 1. HTTPS Configuration
**Mandatory Requirements**:
- TLS 1.2 or higher
- Strong cipher suites only
- HSTS headers enabled
- Certificate pinning (recommended)

**Apache Configuration**:
```apache
SSLEngine on
SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
SSLHonorCipherOrder on
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
```

### 2. Content Security Policy (CSP)
**Strict CSP Headers**:
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https://api.baserow.io;
    connect-src 'self' https://api.baserow.io;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
">
```

### 3. Firewall Configuration
**Required Rules**:
```bash
# Allow HTTPS only
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j REDIRECT --to-port 443

# Block direct HTTP
iptables -A INPUT -p tcp --dport 80 -j REJECT

# Allow SSH for management (restrict source IPs)
iptables -A INPUT -p tcp -s ADMIN_IP --dport 22 -j ACCEPT

# Default deny
iptables -P INPUT DROP
iptables -P FORWARD DROP
```

## Data Protection

### 1. Data Classification
**Sensitivity Levels**:
- **Public**: System documentation, help files
- **Internal**: User interface, general configuration
- **Confidential**: Permit data, audit records
- **Restricted**: User credentials, API tokens

### 2. Encryption Standards
**In Transit**:
- TLS 1.2+ for all communications
- Certificate validation required
- No mixed content allowed

**At Rest**:
- BaseRow handles encryption at rest
- Local storage encrypted (browser dependent)
- Backup encryption required

### 3. Data Retention
**Retention Policies**:
```javascript
const DATA_RETENTION = {
    PERMITS: {
        ACTIVE: 'Indefinite',
        CLOSED: '7 years',
        REJECTED: '3 years'
    },
    AUDITS: {
        ALL: '10 years'
    },
    LOGS: {
        ACCESS: '1 year',
        ERROR: '2 years',
        SECURITY: '5 years'
    }
};
```

## API Security

### 1. BaseRow API Protection
**Token Management**:
```javascript
// Secure token storage (server-side recommended)
const API_SECURITY = {
    TOKEN_ROTATION: 90, // days
    RATE_LIMITING: {
        REQUESTS_PER_MINUTE: 100,
        BURST_LIMIT: 20
    },
    IP_WHITELIST: ['***********/24'], // Internal network only
    REQUIRE_HTTPS: true
};
```

### 2. Input Validation
**Sanitization Rules**:
```javascript
function sanitizeInput(input, type) {
    switch(type) {
        case 'text':
            return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        case 'filename':
            return input.replace(/[^a-zA-Z0-9._-]/g, '');
        case 'number':
            return parseInt(input, 10) || 0;
        default:
            return input.trim();
    }
}
```

### 3. Error Handling
**Secure Error Messages**:
```javascript
function handleAPIError(error) {
    // Log detailed error server-side
    console.error('API Error:', error);
    
    // Return generic message to client
    return {
        success: false,
        message: 'An error occurred. Please try again.',
        code: 'GENERIC_ERROR'
    };
}
```

## File Upload Security

### 1. File Validation
**Security Checks**:
```javascript
function validateUpload(file) {
    const checks = {
        size: file.size <= CONFIG.APP.MAX_FILE_SIZE,
        type: CONFIG.APP.ALLOWED_FILE_TYPES.includes(file.type),
        extension: /\.(jpg|jpeg|png|gif)$/i.test(file.name),
        content: true // Implement content-based validation
    };
    
    return Object.values(checks).every(check => check);
}
```

### 2. File Processing
**Security Measures**:
- Virus scanning (if available)
- Image re-encoding to strip metadata
- File size limits enforced
- Content-type validation

## Monitoring and Logging

### 1. Security Events
**Log Categories**:
```javascript
const SECURITY_EVENTS = {
    LOGIN_SUCCESS: 'User login successful',
    LOGIN_FAILURE: 'User login failed',
    PERMISSION_DENIED: 'Access denied',
    DATA_EXPORT: 'Data exported',
    CONFIG_CHANGE: 'Configuration modified',
    SUSPICIOUS_ACTIVITY: 'Unusual behavior detected'
};
```

### 2. Monitoring Alerts
**Alert Triggers**:
- Multiple failed login attempts
- Access from unusual locations
- Large data downloads
- Configuration changes
- API rate limit exceeded

### 3. Audit Trail
**Required Logging**:
```javascript
function logSecurityEvent(event, user, details) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        event: event,
        user: user?.username || 'anonymous',
        ip: getUserIP(),
        userAgent: navigator.userAgent,
        details: details
    };
    
    // Send to secure logging service
    sendToSecurityLog(logEntry);
}
```

## Incident Response

### 1. Security Incident Classification
**Severity Levels**:
- **Critical**: Data breach, system compromise
- **High**: Unauthorized access, privilege escalation
- **Medium**: Failed security controls, suspicious activity
- **Low**: Policy violations, minor vulnerabilities

### 2. Response Procedures
**Immediate Actions**:
1. Isolate affected systems
2. Preserve evidence
3. Notify security team
4. Document incident
5. Begin containment

**Recovery Steps**:
1. Assess damage scope
2. Restore from clean backups
3. Apply security patches
4. Update security controls
5. Resume operations

### 3. Communication Plan
**Notification Matrix**:
| Incident Level | Internal Notification | External Notification | Timeline |
|----------------|----------------------|----------------------|----------|
| Critical | CISO, IT Director | Legal, Regulators | 1 hour |
| High | Security Team, IT Manager | Legal (if required) | 4 hours |
| Medium | IT Team | None | 24 hours |
| Low | System Admin | None | 72 hours |

## Compliance and Governance

### 1. Regulatory Requirements
**Applicable Standards**:
- ISO 27001 (Information Security)
- NIST Cybersecurity Framework
- Industry-specific regulations
- Local data protection laws

### 2. Security Assessments
**Regular Reviews**:
- Monthly vulnerability scans
- Quarterly penetration testing
- Annual security audits
- Continuous monitoring

### 3. Training and Awareness
**Security Training Topics**:
- Password security
- Phishing awareness
- Data handling procedures
- Incident reporting
- System security features

## Deployment Security Checklist

### Pre-Deployment
- [ ] Security architecture review
- [ ] Vulnerability assessment
- [ ] Penetration testing
- [ ] Code security review
- [ ] Configuration hardening

### Deployment
- [ ] HTTPS properly configured
- [ ] Firewall rules implemented
- [ ] Access controls verified
- [ ] Monitoring enabled
- [ ] Backup procedures tested

### Post-Deployment
- [ ] Security monitoring active
- [ ] Incident response plan ready
- [ ] User training completed
- [ ] Documentation updated
- [ ] Regular review scheduled

## Security Contacts

### Internal Contacts
- **Security Team**: <EMAIL>
- **IT Support**: <EMAIL>
- **Emergency**: +1-XXX-XXX-XXXX

### External Contacts
- **BaseRow Security**: <EMAIL>
- **CERT**: <EMAIL>
- **Legal**: <EMAIL>

## Regular Security Tasks

### Daily
- Monitor security alerts
- Review access logs
- Check system status

### Weekly
- Security patch review
- User access audit
- Backup verification

### Monthly
- Vulnerability scanning
- Security metrics review
- Training updates

### Quarterly
- Penetration testing
- Policy review
- Risk assessment

### Annually
- Security audit
- Disaster recovery test
- Compliance review
