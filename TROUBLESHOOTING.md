# ePTW System Troubleshooting Guide

This guide helps diagnose and resolve common issues with the electronic Permit To Work (ePTW) system.

## Quick Diagnostic Checklist

Before diving into specific issues, run through this quick checklist:

1. **System Status**
   - [ ] Web server is running
   - [ ] HTTPS certificate is valid
   - [ ] BaseRow service is accessible
   - [ ] Network connectivity is stable

2. **Configuration**
   - [ ] API token is valid and not expired
   - [ ] Table IDs are correct in config.js
   - [ ] User accounts exist in BaseRow
   - [ ] Permissions are properly configured

3. **Browser**
   - [ ] JavaScript is enabled
   - [ ] Local storage is available
   - [ ] Browser is supported version
   - [ ] No browser extensions blocking requests

## Common Issues and Solutions

### 1. Login Problems

#### Issue: "Invalid username or password"
**Symptoms**: Users cannot log in with correct credentials

**Possible Causes**:
- Incorrect password hash in database
- Username case sensitivity
- API connection issues

**Solutions**:
1. **Verify User Data**:
   ```javascript
   // Check user record in BaseRow Users table
   // Ensure username matches exactly (case-sensitive)
   // Verify password_hash is properly formatted
   ```

2. **Test API Connection**:
   ```javascript
   // Open browser console and test:
   fetch('https://api.baserow.io/api/database/tables/YOUR_USERS_TABLE_ID/rows/', {
       headers: {
           'Authorization': 'Token YOUR_API_TOKEN'
       }
   })
   .then(response => response.json())
   .then(data => console.log(data));
   ```

3. **Password Hash Generation**:
   ```javascript
   // Use proper bcrypt hashing (server-side recommended)
   // For testing, you can use online bcrypt generators
   // Ensure hash format: $2b$10$...
   ```

#### Issue: "Authentication failed. Please try again."
**Symptoms**: Generic authentication error

**Solutions**:
1. Check browser console for detailed error messages
2. Verify BaseRow API token permissions
3. Confirm network connectivity to BaseRow
4. Check CORS configuration

### 2. Data Loading Issues

#### Issue: Dashboard shows "No data found" or loading indefinitely
**Symptoms**: Empty dashboards, infinite loading spinners

**Possible Causes**:
- API permission issues
- Incorrect table IDs
- Network connectivity problems
- CORS blocking requests

**Solutions**:
1. **Verify Table IDs**:
   ```javascript
   // Check config.js table IDs match BaseRow
   console.log(CONFIG.BASEROW.TABLES);
   
   // Test table access:
   API.getPermits().then(data => console.log(data));
   ```

2. **Check API Permissions**:
   - Ensure API token has read/write access to all tables
   - Verify token hasn't expired
   - Check BaseRow workspace permissions

3. **Network Debugging**:
   ```javascript
   // Test direct API call
   fetch('https://api.baserow.io/api/database/tables/TABLE_ID/rows/', {
       headers: {
           'Authorization': 'Token YOUR_TOKEN',
           'Content-Type': 'application/json'
       }
   })
   .then(response => {
       console.log('Status:', response.status);
       return response.json();
   })
   .then(data => console.log('Data:', data))
   .catch(error => console.error('Error:', error));
   ```

### 3. File Upload Problems

#### Issue: Images won't upload or display
**Symptoms**: File upload fails, images show as broken links

**Solutions**:
1. **Check File Constraints**:
   ```javascript
   // Verify file meets requirements:
   // - Size < 5MB
   // - Type: JPEG, PNG, GIF
   // - Valid file object
   ```

2. **Test Upload Endpoint**:
   ```javascript
   // Test file upload API
   const formData = new FormData();
   formData.append('file', fileInput.files[0]);
   
   fetch('https://api.baserow.io/api/database/tables/TABLE_ID/upload-file/', {
       method: 'POST',
       headers: {
           'Authorization': 'Token YOUR_TOKEN'
       },
       body: formData
   })
   .then(response => response.json())
   .then(data => console.log(data));
   ```

3. **Check BaseRow Storage**:
   - Verify BaseRow account has sufficient storage
   - Check file field configuration in table
   - Ensure file permissions are correct

### 4. Permission and Role Issues

#### Issue: Users see wrong dashboard or get access denied
**Symptoms**: Fireman sees manager dashboard, managers can't access permits

**Solutions**:
1. **Verify User Roles**:
   ```javascript
   // Check user role in session
   const session = Auth.getSession();
   console.log('User role:', session?.user?.role);
   console.log('User department:', session?.user?.department);
   ```

2. **Check Role Logic**:
   ```javascript
   // Verify role checking functions
   console.log('Is Fireman:', Auth.hasRole(CONFIG.ROLES.FIREMAN));
   console.log('Can access dept:', Auth.canAccessDepartment('Electrical'));
   ```

3. **Update User Data**:
   - Correct role assignment in BaseRow Users table
   - Ensure department field matches exactly
   - Clear browser local storage and re-login

### 5. Performance Issues

#### Issue: Slow loading, timeouts, unresponsive interface
**Symptoms**: Long wait times, browser freezing, timeout errors

**Solutions**:
1. **Check Network Performance**:
   ```javascript
   // Measure API response times
   const start = performance.now();
   API.getPermits().then(() => {
       const end = performance.now();
       console.log('API call took:', end - start, 'ms');
   });
   ```

2. **Optimize Data Loading**:
   ```javascript
   // Reduce refresh frequency if needed
   CONFIG.APP.REFRESH_INTERVAL = 60000; // 1 minute
   
   // Implement pagination for large datasets
   // Filter data on server side when possible
   ```

3. **Browser Optimization**:
   - Clear browser cache and cookies
   - Disable unnecessary browser extensions
   - Close other tabs/applications
   - Use latest browser version

### 6. Session and Logout Issues

#### Issue: Users logged out unexpectedly
**Symptoms**: Frequent redirects to login page, session timeouts

**Solutions**:
1. **Check Session Configuration**:
   ```javascript
   // Verify session timeout setting
   console.log('Session timeout:', CONFIG.APP.SESSION_TIMEOUT);
   
   // Check current session
   const session = Auth.getSession();
   console.log('Session expires:', new Date(session?.expiresAt));
   ```

2. **Extend Session Timeout**:
   ```javascript
   // In config.js, increase timeout for production
   SESSION_TIMEOUT: 8 * 60 * 60 * 1000, // 8 hours
   ```

3. **Clear Corrupted Sessions**:
   ```javascript
   // Clear local storage
   localStorage.removeItem('eptw_session');
   // Refresh page
   window.location.reload();
   ```

## Debug Mode Usage

Enable debug mode for detailed troubleshooting:

1. **Activate Debug Mode**:
   ```
   https://your-domain.com/eptw/index.html?debug=true
   ```

2. **Debug Panel Features**:
   - Real-time console logging
   - Configuration validation
   - API call monitoring
   - Session state tracking

3. **Console Commands**:
   ```javascript
   // Test configuration
   validateConfig();
   
   // Check current user
   console.log(AppState.currentUser);
   
   // Test API endpoints
   API.getUsers().then(console.log);
   API.getPermits().then(console.log);
   API.getAudits().then(console.log);
   
   // Clear all data
   localStorage.clear();
   ```

## Browser-Specific Issues

### Chrome
- **Issue**: CORS errors in console
- **Solution**: Ensure proper HTTPS setup, check BaseRow CORS settings

### Firefox
- **Issue**: Local storage not persisting
- **Solution**: Check privacy settings, ensure cookies are enabled

### Safari
- **Issue**: File upload not working
- **Solution**: Update to latest Safari version, check file input compatibility

### Edge
- **Issue**: CSS styling issues
- **Solution**: Verify CSS compatibility, check for IE mode

## Network and Infrastructure Issues

### 1. Firewall Problems
**Symptoms**: API calls fail, connection timeouts

**Solutions**:
- Whitelist BaseRow API domains
- Allow HTTPS traffic (port 443)
- Check corporate proxy settings

### 2. DNS Issues
**Symptoms**: Cannot reach BaseRow API

**Solutions**:
- Verify DNS resolution: `nslookup api.baserow.io`
- Try alternative DNS servers
- Check hosts file for conflicts

### 3. SSL Certificate Problems
**Symptoms**: Security warnings, HTTPS errors

**Solutions**:
- Verify certificate validity and expiration
- Check certificate chain
- Ensure proper domain matching

## BaseRow-Specific Issues

### 1. API Rate Limiting
**Symptoms**: 429 Too Many Requests errors

**Solutions**:
- Implement request throttling
- Reduce refresh frequency
- Upgrade BaseRow plan if needed

### 2. Storage Quota Exceeded
**Symptoms**: File uploads fail, 413 errors

**Solutions**:
- Check BaseRow storage usage
- Clean up old files
- Upgrade storage plan

### 3. Table Schema Changes
**Symptoms**: Field not found errors, data type mismatches

**Solutions**:
- Verify table structure matches documentation
- Check field names and types
- Update application if schema changed

## Emergency Procedures

### 1. System Down
1. Check web server status
2. Verify BaseRow service status
3. Test network connectivity
4. Review recent changes
5. Implement rollback if necessary

### 2. Data Corruption
1. Stop all write operations
2. Assess damage scope
3. Restore from latest backup
4. Verify data integrity
5. Resume operations

### 3. Security Breach
1. Immediately revoke API tokens
2. Change all passwords
3. Review access logs
4. Implement additional security measures
5. Document incident

## Getting Help

### 1. Internal Support
- Check system logs first
- Document error messages
- Note steps to reproduce
- Include browser and OS information

### 2. BaseRow Support
- Visit: https://baserow.io/support
- Include API token (redacted)
- Provide table IDs and error messages
- Describe expected vs actual behavior

### 3. Community Resources
- BaseRow Community Forum
- GitHub Issues (if applicable)
- Stack Overflow with 'baserow' tag

## Preventive Measures

### 1. Regular Monitoring
- Set up automated health checks
- Monitor API response times
- Track error rates
- Review user feedback

### 2. Maintenance Schedule
- Weekly log reviews
- Monthly security updates
- Quarterly backup tests
- Annual security audits

### 3. Documentation
- Keep troubleshooting log
- Document configuration changes
- Update user guides
- Maintain contact information
