<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ePTW System - Fireman Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body class="dashboard-page">
    <!-- Navigation Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="header-left">
                <div class="logo-section">
                    <span class="logo-icon">🛡️</span>
                    <h1>ePTW System</h1>
                </div>
                <nav class="main-nav">
                    <button class="nav-button active" data-section="overview">
                        <span class="nav-icon">📊</span>
                        Overview
                    </button>
                    <button class="nav-button" data-section="permits">
                        <span class="nav-icon">📋</span>
                        Permits
                    </button>
                    <button class="nav-button" data-section="audits">
                        <span class="nav-icon">🔍</span>
                        Audits
                    </button>
                </nav>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-role">👨‍🚒</span>
                    <div class="user-details">
                        <span class="user-name" id="userName">Fireman</span>
                        <span class="user-role-text">Safety Officer</span>
                    </div>
                </div>
                <button class="refresh-button" id="refreshButton" title="Refresh Data">
                    <span class="refresh-icon">🔄</span>
                </button>
                <button class="logout-button" id="logoutButton" title="Logout">
                    <span class="logout-icon">🚪</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="dashboard-main">
        <!-- Overview Section -->
        <section id="overviewSection" class="dashboard-section active">
            <div class="section-header">
                <h2>Dashboard Overview</h2>
                <p class="section-subtitle">Monitor permit status and safety compliance</p>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <h3 id="totalPermits">0</h3>
                        <p>Total Permits</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <h3 id="pendingPermits">0</h3>
                        <p>Pending Approval</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 id="approvedPermits">0</h3>
                        <p>Approved</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🔍</div>
                    <div class="stat-content">
                        <h3 id="totalAudits">0</h3>
                        <p>Audits Conducted</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <button class="action-button primary" id="createPermitBtn">
                        <span class="action-icon">➕</span>
                        <div class="action-content">
                            <h4>Create Permit</h4>
                            <p>Issue new work permit</p>
                        </div>
                    </button>
                    <button class="action-button secondary" id="createAuditBtn">
                        <span class="action-icon">🔍</span>
                        <div class="action-content">
                            <h4>Create Audit</h4>
                            <p>Conduct safety audit</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <h3>Recent Activity</h3>
                <div id="recentActivityList" class="activity-list">
                    <!-- Activity items will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Permits Section -->
        <section id="permitsSection" class="dashboard-section">
            <div class="section-header">
                <h2>Permit Management</h2>
                <div class="section-actions">
                    <div class="search-box">
                        <input type="text" id="permitSearch" placeholder="Search permits..." class="search-input">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="permitFilter" class="filter-select">
                        <option value="">All Departments</option>
                        <option value="Electrical">Electrical</option>
                        <option value="Mechanical">Mechanical</option>
                        <option value="Civil">Civil</option>
                    </select>
                    <button class="primary-button" id="newPermitBtn">
                        <span class="button-icon">➕</span>
                        New Permit
                    </button>
                </div>
            </div>

            <!-- Permits List -->
            <div id="permitsList" class="permits-list">
                <!-- Permits will be populated by JavaScript -->
            </div>
        </section>

        <!-- Audits Section -->
        <section id="auditsSection" class="dashboard-section">
            <div class="section-header">
                <h2>Audit Management</h2>
                <div class="section-actions">
                    <div class="search-box">
                        <input type="text" id="auditSearch" placeholder="Search audits..." class="search-input">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="auditFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="Pass">Pass</option>
                        <option value="Fail">Fail</option>
                    </select>
                    <button class="primary-button" id="newAuditBtn">
                        <span class="button-icon">🔍</span>
                        New Audit
                    </button>
                </div>
            </div>

            <!-- Audits List -->
            <div id="auditsList" class="audits-list">
                <!-- Audits will be populated by JavaScript -->
            </div>
        </section>
    </main>

    <!-- Create Permit Modal -->
    <div id="createPermitModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Permit</h3>
                <button class="modal-close" id="closePermitModal">&times;</button>
            </div>
            <form id="createPermitForm" class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="workType">Work Type *</label>
                        <input type="text" id="workType" name="workType" required class="form-input" placeholder="e.g., Electrical maintenance">
                    </div>
                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" required class="form-input" placeholder="e.g., Building A - Floor 2">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="requestedBy">Requested By *</label>
                        <input type="text" id="requestedBy" name="requestedBy" required class="form-input" placeholder="Person requesting work">
                    </div>
                    <div class="form-group">
                        <label for="department">Department *</label>
                        <select id="department" name="department" required class="form-select">
                            <option value="">Select Department</option>
                            <option value="Electrical">Electrical</option>
                            <option value="Mechanical">Mechanical</option>
                            <option value="Civil">Civil</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="permitImage">Attach Image (Optional)</label>
                    <input type="file" id="permitImage" name="permitImage" accept="image/*" class="form-file">
                    <div class="file-info">
                        <small>Maximum file size: 5MB. Supported formats: JPEG, PNG, GIF</small>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="secondary-button" id="cancelPermit">Cancel</button>
                    <button type="submit" class="primary-button" id="submitPermit">
                        <span class="button-text">Create Permit</span>
                        <span class="button-spinner hidden">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Create Audit Modal -->
    <div id="createAuditModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Audit</h3>
                <button class="modal-close" id="closeAuditModal">&times;</button>
            </div>
            <form id="createAuditForm" class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="permitId">Permit *</label>
                        <select id="permitId" name="permitId" required class="form-select">
                            <option value="">Select Permit to Audit</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="auditorName">Auditor Name *</label>
                        <input type="text" id="auditorName" name="auditorName" required class="form-input" placeholder="Your name">
                    </div>
                </div>
                <div class="form-group">
                    <label for="remarks">Audit Remarks *</label>
                    <textarea id="remarks" name="remarks" required class="form-textarea" rows="4" placeholder="Detailed audit findings and observations..."></textarea>
                </div>
                <div class="form-group">
                    <label for="complianceStatus">Compliance Status *</label>
                    <select id="complianceStatus" name="complianceStatus" required class="form-select">
                        <option value="">Select Status</option>
                        <option value="Pass">Pass</option>
                        <option value="Fail">Fail</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="secondary-button" id="cancelAudit">Cancel</button>
                    <button type="submit" class="primary-button" id="submitAudit">
                        <span class="button-text">Create Audit</span>
                        <span class="button-spinner hidden">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="script.js"></script>
    <script>
        // Initialize fireman dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeFiremanDashboard();
        });
    </script>
</body>
</html>
