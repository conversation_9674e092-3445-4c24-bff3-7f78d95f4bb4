# ePTW Database Schema Documentation

This document provides detailed specifications for the BaseRow database schema used in the electronic Permit To Work (ePTW) system.

## Database Overview

The ePTW system uses three main tables to manage permits, audits, and user authentication:

1. **Users** - Authentication and role management
2. **Permits** - Work permit records and lifecycle
3. **Audits** - Safety audit records linked to permits

## Table Specifications

### 1. Users Table

**Purpose**: Manages user authentication and role-based access control

| Field Name | Type | Required | Unique | Default | Description |
|------------|------|----------|--------|---------|-------------|
| id | Auto Number | Yes | Yes | Auto | Primary key, auto-generated |
| username | Single Line Text | Yes | Yes | - | Unique username for login |
| password_hash | Single Line Text | Yes | No | - | Bcrypt hashed password |
| role | Single Select | Yes | No | - | User role for access control |
| department | Single Line Text | No | No | null | Department assignment (null for Fireman) |

**Single Select Options for 'role':**
- `Fireman` - Can create permits and audits, view all departments
- `Electrical_Manager` - Can approve/reject electrical permits
- `Mechanical_Manager` - Can approve/reject mechanical permits  
- `Civil_Manager` - Can approve/reject civil permits

**Business Rules:**
- Username must be unique across all users
- Password must be bcrypt hashed with minimum 10 rounds
- Fireman role should have null department
- Manager roles must have matching department assignment

**Sample Data:**
```json
{
  "username": "fireman1",
  "password_hash": "$2b$10$N9qo8uLOickgx2ZMRZoMye...",
  "role": "Fireman",
  "department": null
}
```

### 2. Permits Table

**Purpose**: Stores work permit information and tracks approval workflow

| Field Name | Type | Required | Default | Description |
|------------|------|----------|---------|-------------|
| id | Auto Number | Yes | Auto | Primary key, auto-generated |
| work_type | Single Line Text | Yes | - | Type of work to be performed |
| location | Single Line Text | Yes | - | Work location/area |
| requested_by | Single Line Text | Yes | - | Person requesting the permit |
| department | Single Select | Yes | - | Department responsible for work |
| status | Single Select | Yes | Pending | Current permit status |
| image | File | No | - | Optional image attachment |
| created_at | Date | Yes | Auto | Permit creation timestamp |
| updated_at | Date | Yes | Auto | Last modification timestamp |

**Single Select Options for 'department':**
- `Electrical` - Electrical work permits
- `Mechanical` - Mechanical work permits
- `Civil` - Civil/structural work permits

**Single Select Options for 'status':**
- `Pending` - Awaiting manager approval (default)
- `Approved` - Approved by department manager
- `Rejected` - Rejected by department manager
- `Closed` - Work completed and permit closed

**File Field Configuration for 'image':**
- Allow multiple files: No
- Maximum file size: 5MB
- Allowed types: JPEG, PNG, GIF
- Storage: BaseRow file storage

**Business Rules:**
- Status defaults to 'Pending' on creation
- Only department managers can change status to Approved/Rejected
- Firemen can create permits for any department
- Images are optional but recommended for documentation

**Sample Data:**
```json
{
  "work_type": "Electrical panel maintenance",
  "location": "Building A - Floor 2 - Room 201",
  "requested_by": "John Smith",
  "department": "Electrical",
  "status": "Pending",
  "created_at": "2024-01-15T10:30:00Z"
}
```

### 3. Audits Table

**Purpose**: Records safety audits performed on permits

| Field Name | Type | Required | Default | Description |
|------------|------|----------|---------|-------------|
| id | Auto Number | Yes | Auto | Primary key, auto-generated |
| permit_id | Link to Table | Yes | - | Links to Permits table |
| auditor_name | Single Line Text | Yes | - | Name of person conducting audit |
| remarks | Long Text | Yes | - | Detailed audit findings and notes |
| compliance_status | Single Select | Yes | - | Pass/Fail compliance result |
| audit_date | Date | Yes | Auto | Date audit was conducted |

**Link Configuration for 'permit_id':**
- Links to: Permits table
- Link type: One-to-many (one permit can have multiple audits)
- Display field: Show permit work_type and location

**Single Select Options for 'compliance_status':**
- `Pass` - Audit passed, work complies with safety standards
- `Fail` - Audit failed, safety issues identified

**Business Rules:**
- Each audit must be linked to an existing permit
- Multiple audits can be conducted for the same permit
- Only Firemen can create audit records
- Audit date auto-fills with current date/time

**Sample Data:**
```json
{
  "permit_id": 123,
  "auditor_name": "Safety Officer Mike",
  "remarks": "All safety protocols followed. PPE properly used. Work area secured.",
  "compliance_status": "Pass",
  "audit_date": "2024-01-16T14:15:00Z"
}
```

## Relationships

### Primary Relationships
1. **Audits → Permits**: Many-to-one relationship via permit_id
2. **Users → Departments**: Logical relationship via department field

### Data Flow
1. Fireman creates permit → Status: Pending
2. Department manager reviews → Status: Approved/Rejected
3. Work performed → Fireman conducts audit
4. Permit closed → Status: Closed

## Indexes and Performance

**Recommended Indexes:**
- Users.username (unique index for fast login lookup)
- Permits.department (for manager dashboard filtering)
- Permits.status (for status-based queries)
- Permits.created_at (for chronological sorting)
- Audits.permit_id (for permit-audit relationship queries)

## Data Validation Rules

### Client-Side Validation
- All required fields must be filled
- File uploads must meet size/type restrictions
- Date fields must be valid dates
- Text fields must not exceed reasonable length limits

### Server-Side Validation
- Username uniqueness enforcement
- Password complexity requirements
- File type and size validation
- Role-department consistency checks

## Security Considerations

### Data Protection
- Password hashes never stored in plain text
- API tokens secured and rotated regularly
- File uploads scanned for malicious content
- Access logs maintained for audit trail

### Access Control
- Role-based data filtering at API level
- Department managers only see their department's permits
- Firemen have read access to all departments
- No direct database access for end users

## Backup and Recovery

### Backup Strategy
- Daily automated backups via BaseRow
- Export critical data weekly to CSV
- Maintain 30-day backup retention
- Test restore procedures monthly

### Recovery Procedures
1. Identify data loss scope
2. Restore from most recent clean backup
3. Replay transactions if possible
4. Validate data integrity post-recovery

## Migration Considerations

### Future Schema Changes
- Use BaseRow's field addition capabilities
- Maintain backward compatibility
- Document all schema modifications
- Test changes in staging environment first

### Data Migration
- Export existing data before major changes
- Use BaseRow API for bulk data operations
- Validate migrated data completeness
- Maintain rollback procedures
