<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ePTW System - Login</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body class="login-page">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p>Authenticating...</p>
    </div>

    <!-- Main Login Container -->
    <div class="login-container">
        <!-- Header Section -->
        <header class="login-header">
            <div class="logo-section">
                <div class="logo-icon">🛡️</div>
                <h1>ePTW System</h1>
                <p class="subtitle">Electronic Permit To Work</p>
            </div>
        </header>

        <!-- Login Form -->
        <main class="login-main">
            <div class="login-card">
                <h2>Sign In</h2>
                <p class="login-description">Access your permit management dashboard</p>
                
                <!-- Error Message Display -->
                <div id="errorMessage" class="error-message hidden">
                    <span class="error-icon">⚠️</span>
                    <span id="errorText">Invalid credentials. Please try again.</span>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            required 
                            autocomplete="username"
                            placeholder="Enter your username"
                            class="form-input"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            autocomplete="current-password"
                            placeholder="Enter your password"
                            class="form-input"
                        >
                    </div>

                    <button type="submit" class="login-button" id="loginButton">
                        <span class="button-text">Sign In</span>
                        <span class="button-spinner hidden">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </form>

                <p class="signup-link">
                    <a href="signup.html">Don't have an account? Sign up</a>
                </p>

                <!-- Role Information -->
                <div class="role-info">
                    <h3>System Roles</h3>
                    <div class="role-grid">
                        <div class="role-card">
                            <div class="role-icon">👨‍🚒</div>
                            <h4>Fireman</h4>
                            <p>Create permits and conduct audits</p>
                        </div>
                        <div class="role-card">
                            <div class="role-icon">⚡</div>
                            <h4>Electrical Manager</h4>
                            <p>Approve electrical permits</p>
                        </div>
                        <div class="role-card">
                            <div class="role-icon">⚙️</div>
                            <h4>Mechanical Manager</h4>
                            <p>Approve mechanical permits</p>
                        </div>
                        <div class="role-card">
                            <div class="role-icon">🏗️</div>
                            <h4>Civil Manager</h4>
                            <p>Approve civil permits</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="login-footer">
            <p>&copy; 2024 Industrial Safety Systems. All rights reserved.</p>
            <p class="version">Version 1.0.0</p>
        </footer>
    </div>

    <!-- Debug Panel (hidden by default) -->
    <div id="debugPanel" class="debug-panel hidden">
        <h4>Debug Information</h4>
        <div id="debugContent"></div>
        <button onclick="toggleDebug()" class="debug-close">Close</button>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script>
        // Global variables
        let isDebugMode = false;

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // Check for debug mode
            const urlParams = new URLSearchParams(window.location.search);
            isDebugMode = urlParams.get('debug') === 'true';
            
            if (isDebugMode) {
                enableDebugMode();
            }

            // Validate configuration
            if (!validateConfig()) {
                showError('System configuration incomplete. Please contact administrator.');
                return;
            }

            // Check if user is already logged in
            checkExistingSession();

            // Setup form handlers
            setupEventListeners();

            // Focus username field
            document.getElementById('username').focus();
        }

        function setupEventListeners() {
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);

            // Clear error on input
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('input', clearError);
            });

            // Handle Enter key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.target.closest('form')) {
                    document.getElementById('username').focus();
                }
            });
        }

        async function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showError('Please enter both username and password.');
                return;
            }

            setLoading(true);
            clearError();

            try {
                const user = await authenticateUser(username, password);
                
                if (user) {
                    // Store session
                    storeUserSession(user);
                    
                    // Redirect based on role
                    redirectToDashboard(user.role);
                } else {
                    showError('Invalid username or password.');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Login failed. Please check your connection and try again.');
            } finally {
                setLoading(false);
            }
        }

        async function authenticateUser(username, password) {
            debugLog('Attempting authentication for user:', username);

            try {
                // Use the Auth module from script.js
                const user = await Auth.authenticate(username, password);
                return user;
            } catch (error) {
                debugLog('Authentication error:', error);
                throw error;
            }
        }

        function storeUserSession(user) {
            const sessionData = {
                user: user,
                loginTime: Date.now(),
                expiresAt: Date.now() + CONFIG.APP.SESSION_TIMEOUT
            };
            
            localStorage.setItem('eptw_session', JSON.stringify(sessionData));
            debugLog('Session stored for user:', user.username);
        }

        function checkExistingSession() {
            const sessionData = localStorage.getItem('eptw_session');
            
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    
                    if (session.expiresAt > Date.now()) {
                        // Valid session exists, redirect to dashboard
                        redirectToDashboard(session.user.role);
                        return;
                    } else {
                        // Session expired, clear it
                        localStorage.removeItem('eptw_session');
                    }
                } catch (error) {
                    // Invalid session data, clear it
                    localStorage.removeItem('eptw_session');
                }
            }
        }

        function redirectToDashboard(role) {
            if (role === CONFIG.ROLES.FIREMAN) {
                window.location.href = 'fireman-dashboard.html';
            } else if (role.includes('Manager')) {
                window.location.href = 'manager-dashboard.html';
            } else {
                showError('Invalid user role. Please contact administrator.');
            }
        }

        function setLoading(loading) {
            const overlay = document.getElementById('loadingOverlay');
            const button = document.getElementById('loginButton');
            const buttonText = button.querySelector('.button-text');
            const buttonSpinner = button.querySelector('.button-spinner');

            if (loading) {
                overlay.classList.remove('hidden');
                button.disabled = true;
                buttonText.classList.add('hidden');
                buttonSpinner.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
                button.disabled = false;
                buttonText.classList.remove('hidden');
                buttonSpinner.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
            
            // Auto-hide after 5 seconds
            setTimeout(clearError, 5000);
        }

        function clearError() {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.classList.add('hidden');
        }

        function enableDebugMode() {
            document.body.classList.add('debug-mode');
            const debugPanel = document.getElementById('debugPanel');
            debugPanel.classList.remove('hidden');
            
            debugLog('Debug mode enabled');
            debugLog('Configuration:', CONFIG);
        }

        function debugLog(message, data = null) {
            if (!isDebugMode) return;
            
            console.log('[ePTW Debug]', message, data);
            
            const debugContent = document.getElementById('debugContent');
            const logEntry = document.createElement('div');
            logEntry.className = 'debug-entry';
            logEntry.innerHTML = `
                <span class="debug-time">${new Date().toLocaleTimeString()}</span>
                <span class="debug-message">${message}</span>
                ${data ? `<pre class="debug-data">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            debugContent.appendChild(logEntry);
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        function toggleDebug() {
            const debugPanel = document.getElementById('debugPanel');
            debugPanel.classList.toggle('hidden');
        }
    </script>
</body>
</html>

