# Electronic Permit To Work (ePTW) System

A comprehensive web-based Permit To Work system designed for industrial facilities, built with vanilla HTML, CSS, and JavaScript with BaseRow as the backend database.

## Features

### User Roles
- **Fireman**: Creates permits and conducts safety audits
- **Department Managers**: Approve/reject permits for their specific departments (Electrical, Mechanical, Civil)

### Core Functionality
- **Permit Management**: Create, view, approve, and track work permits
- **Audit System**: Conduct and record safety audits for permits
- **Role-Based Access**: Department-specific dashboards and permissions
- **File Upload**: Attach images to permits for documentation
- **Real-time Updates**: Auto-refresh dashboards for current information
- **Mobile Responsive**: Works on tablets and mobile devices

## Technology Stack

- **Frontend**: Vanilla HTML5, CSS3, JavaScript (ES6+)
- **Backend**: BaseRow REST API
- **Database**: BaseRow cloud database
- **Authentication**: Token-based with session management
- **Styling**: Dark theme with Apple-inspired design

## Project Structure

```
ePTW/
├── index.html              # Login page
├── fireman-dashboard.html  # Fireman dashboard
├── manager-dashboard.html  # Manager dashboard
├── styles.css             # Unified stylesheet
├── script.js              # Main JavaScript functionality
├── config.js              # API configuration
├── BASEROW_SETUP.md       # Database setup instructions
├── README.md              # This file
└── DEPLOYMENT.md          # Deployment guide
```

## Quick Start

### 1. Database Setup
1. Follow instructions in `BASEROW_SETUP.md` to create BaseRow database
2. Create required tables: Users, Permits, Audits
3. Generate API token and note table IDs

### 2. Configuration
1. Open `config.js`
2. Replace placeholder values with your BaseRow credentials:
   - API token
   - Database ID
   - Table IDs

### 3. Initial Users
Create initial user accounts in BaseRow Users table with proper password hashes.

### 4. Deployment
1. Upload all files to your web server
2. Ensure HTTPS is enabled for security
3. Configure proper CORS settings if needed

## User Guide

### For Firemen
1. **Login**: Use fireman credentials on login page
2. **Create Permit**: Fill out work details, select department, upload images
3. **Create Audit**: Select permit, add audit findings and compliance status
4. **Dashboard**: View all permits and audits across departments

### For Department Managers
1. **Login**: Use department-specific manager credentials
2. **Review Permits**: View permits filtered to your department
3. **Approve/Reject**: Make decisions on pending permits
4. **Track Status**: Monitor permit lifecycle and compliance

## Security Features

- **Role-based access control**: Users only see relevant data
- **Session management**: Automatic logout after inactivity
- **Input validation**: Client-side and server-side validation
- **Secure file upload**: File type and size restrictions
- **API token security**: Tokens stored securely, not in client code

## Browser Compatibility

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## API Endpoints

The system uses BaseRow REST API endpoints:

- `GET /api/database/tables/{table_id}/rows/` - Fetch records
- `POST /api/database/tables/{table_id}/rows/` - Create records
- `PATCH /api/database/tables/{table_id}/rows/{row_id}/` - Update records
- `POST /api/database/tables/{table_id}/upload-file/` - Upload files

## Development

### Local Development
1. Serve files using a local web server (Python, Node.js, or any HTTP server)
2. Ensure CORS is properly configured for BaseRow API calls
3. Use browser developer tools for debugging

### Testing
- Test all user roles and permissions
- Verify file upload functionality
- Check responsive design on various devices
- Validate form submissions and error handling

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check API token validity
   - Verify table IDs are correct
   - Ensure CORS is properly configured

2. **Login Issues**
   - Verify user exists in Users table
   - Check password hash format
   - Confirm role assignment

3. **File Upload Problems**
   - Check file size limits
   - Verify file type restrictions
   - Ensure proper API permissions

### Debug Mode
Enable debug mode by adding `?debug=true` to any page URL for additional console logging.

## Support

For technical support:
1. Check browser console for error messages
2. Verify BaseRow API status
3. Review configuration settings
4. Consult troubleshooting section

## License

This software is designed for internal corporate use. Ensure compliance with your organization's software policies.

## Version History

- **v1.0.0**: Initial release with core functionality
  - User authentication and role management
  - Permit creation and approval workflow
  - Audit system implementation
  - File upload capabilities
  - Responsive design implementation
