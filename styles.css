/* ePTW System - Dark Theme Stylesheet */
/* Apple-inspired design with industrial safety focus */

/* CSS Variables for consistent theming */
:root {
    /* Color Palette */
    --primary-bg: #000000;
    --secondary-bg: #1c1c1e;
    --tertiary-bg: #2c2c2e;
    --quaternary-bg: #3a3a3c;
    --surface-bg: #48484a;
    
    --primary-text: #ffffff;
    --secondary-text: #e5e5e7;
    --tertiary-text: #aeaeb2;
    --quaternary-text: #8e8e93;
    
    --accent-blue: #007aff;
    --accent-green: #30d158;
    --accent-orange: #ff9500;
    --accent-red: #ff3b30;
    --accent-yellow: #ffcc00;
    
    --success-color: #30d158;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --info-color: #007aff;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.25s ease-out;
    --transition-slow: 0.35s ease-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background-color: var(--primary-bg);
    color: var(--primary-text);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Login Page Styles */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.login-container {
    width: 100%;
    max-width: 480px;
    padding: var(--spacing-lg);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-sm);
}

.login-header h1 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.subtitle {
    color: var(--tertiary-text);
    font-size: var(--font-size-md);
}

.login-card {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--tertiary-bg);
}

.login-card h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.login-description {
    color: var(--tertiary-text);
    margin-bottom: var(--spacing-lg);
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-text);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--tertiary-bg);
    border: 1px solid var(--quaternary-bg);
    border-radius: var(--radius-md);
    color: var(--primary-text);
    font-size: var(--font-size-md);
    transition: var(--transition-normal);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-input::placeholder {
    color: var(--quaternary-text);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-file {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--tertiary-bg);
    border: 2px dashed var(--quaternary-bg);
    border-radius: var(--radius-md);
    color: var(--secondary-text);
    cursor: pointer;
    transition: var(--transition-normal);
}

.form-file:hover {
    border-color: var(--accent-blue);
    background: var(--quaternary-bg);
}

.file-info {
    margin-top: var(--spacing-sm);
}

.file-info small {
    color: var(--quaternary-text);
    font-size: var(--font-size-xs);
}

/* Button Styles */
.login-button,
.primary-button,
.secondary-button,
.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.login-button,
.primary-button {
    background: var(--accent-blue);
    color: white;
}

.login-button:hover,
.primary-button:hover {
    background: #0056cc;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.secondary-button {
    background: var(--tertiary-bg);
    color: var(--primary-text);
    border: 1px solid var(--quaternary-bg);
}

.secondary-button:hover {
    background: var(--quaternary-bg);
    transform: translateY(-1px);
}

.login-button {
    width: 100%;
    margin-bottom: var(--spacing-lg);
}

.button-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error Message Styles */
.error-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid var(--error-color);
    border-radius: var(--radius-md);
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
}

.error-icon {
    font-size: var(--font-size-lg);
}

/* Role Information */
.role-info {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--tertiary-bg);
}

.role-info h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.role-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.role-card {
    background: var(--tertiary-bg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    text-align: center;
    border: 1px solid var(--quaternary-bg);
    transition: var(--transition-normal);
}

.role-card:hover {
    background: var(--quaternary-bg);
    transform: translateY(-2px);
}

.role-icon {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.role-card h4 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.role-card p {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--tertiary-bg);
    color: var(--quaternary-text);
    font-size: var(--font-size-sm);
}

.version {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--quaternary-bg);
    border-top: 3px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-overlay p {
    color: var(--secondary-text);
    font-size: var(--font-size-md);
}

/* Debug Panel */
.debug-panel {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 300px;
    max-height: 400px;
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.debug-panel h4 {
    margin-bottom: var(--spacing-md);
    color: var(--accent-orange);
}

#debugContent {
    max-height: 300px;
    overflow-y: auto;
    font-size: var(--font-size-xs);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.debug-entry {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-xs);
    background: var(--tertiary-bg);
    border-radius: var(--radius-sm);
}

.debug-time {
    color: var(--quaternary-text);
    font-size: 10px;
}

.debug-message {
    color: var(--secondary-text);
    display: block;
}

.debug-data {
    color: var(--tertiary-text);
    font-size: 10px;
    margin-top: var(--spacing-xs);
    white-space: pre-wrap;
}

.debug-close {
    margin-top: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--accent-orange);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: var(--font-size-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        padding: var(--spacing-md);
    }
    
    .login-card {
        padding: var(--spacing-lg);
    }
    
    .role-grid {
        grid-template-columns: 1fr;
    }
    
    .debug-panel {
        width: calc(100% - 2 * var(--spacing-md));
        right: var(--spacing-md);
        left: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .login-header h1 {
        font-size: var(--font-size-xl);
    }

    .logo-icon {
        font-size: 36px;
    }

    .login-card {
        padding: var(--spacing-md);
    }
}

/* Dashboard Layout */
.dashboard-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-header {
    background: var(--secondary-bg);
    border-bottom: 1px solid var(--tertiary-bg);
    padding: var(--spacing-md) var(--spacing-lg);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-section h1 {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.logo-section .logo-icon {
    font-size: var(--font-size-xl);
}

/* Navigation */
.main-nav {
    display: flex;
    gap: var(--spacing-xs);
}

.nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-normal);
}

.nav-button:hover {
    background: var(--tertiary-bg);
    color: var(--secondary-text);
}

.nav-button.active {
    background: var(--accent-blue);
    color: white;
}

.nav-icon {
    font-size: var(--font-size-md);
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-role {
    font-size: var(--font-size-lg);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.user-role-text {
    color: var(--tertiary-text);
    font-size: var(--font-size-xs);
}

/* Department Badge */
.department-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--tertiary-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--quaternary-bg);
}

.dept-icon {
    font-size: var(--font-size-md);
}

.dept-name {
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Header Buttons */
.refresh-button,
.logout-button {
    padding: var(--spacing-sm);
    background: var(--tertiary-bg);
    border: 1px solid var(--quaternary-bg);
    border-radius: var(--radius-md);
    color: var(--secondary-text);
    cursor: pointer;
    transition: var(--transition-normal);
}

.refresh-button:hover,
.logout-button:hover {
    background: var(--quaternary-bg);
    transform: translateY(-1px);
}

.logout-button:hover {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

/* Main Dashboard Content */
.dashboard-main {
    flex: 1;
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.section-subtitle {
    color: var(--tertiary-text);
    font-size: var(--font-size-md);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card.pending {
    border-left: 4px solid var(--accent-orange);
}

.stat-card.approved {
    border-left: 4px solid var(--success-color);
}

.stat-card.rejected {
    border-left: 4px solid var(--error-color);
}

.stat-card.total {
    border-left: 4px solid var(--accent-blue);
}

.stat-icon {
    font-size: var(--font-size-xl);
    opacity: 0.8;
}

.stat-content h3 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

.stat-action {
    margin-left: auto;
}

.stat-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--accent-blue);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition-fast);
}

.stat-button:hover {
    background: #0056cc;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-xl);
}

.quick-actions h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.action-button {
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: left;
}

.action-button:hover {
    background: var(--tertiary-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-button.primary {
    border-color: var(--accent-blue);
}

.action-button.secondary {
    border-color: var(--accent-green);
}

.action-icon {
    font-size: var(--font-size-xl);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tertiary-bg);
    border-radius: var(--radius-md);
}

.action-content h4 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.action-content p {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

/* Search and Filter */
.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
    background: var(--tertiary-bg);
    border: 1px solid var(--quaternary-bg);
    border-radius: var(--radius-md);
    color: var(--primary-text);
    font-size: var(--font-size-sm);
}

.search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--quaternary-text);
    pointer-events: none;
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--tertiary-bg);
    border: 1px solid var(--quaternary-bg);
    border-radius: var(--radius-md);
    color: var(--primary-text);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* Permits Grid */
.permits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.permit-card {
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.permit-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.permit-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.permit-title {
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.permit-id {
    color: var(--quaternary-text);
    font-size: var(--font-size-xs);
    font-family: monospace;
}

.permit-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.permit-status.pending {
    background: rgba(255, 149, 0, 0.2);
    color: var(--accent-orange);
    border: 1px solid var(--accent-orange);
}

.permit-status.approved {
    background: rgba(48, 209, 88, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.permit-status.rejected {
    background: rgba(255, 59, 48, 0.2);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.permit-status.closed {
    background: rgba(142, 142, 147, 0.2);
    color: var(--quaternary-text);
    border: 1px solid var(--quaternary-text);
}

.permit-details {
    margin-bottom: var(--spacing-md);
}

.permit-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.permit-detail-label {
    color: var(--tertiary-text);
}

.permit-detail-value {
    font-weight: 500;
}

.permit-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
}

.permit-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.permit-action-btn {
    flex: 1;
    padding: var(--spacing-sm);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
}

.approve-button {
    background: var(--success-color);
    color: white;
}

.approve-button:hover {
    background: #28a745;
}

.reject-button {
    background: var(--error-color);
    color: white;
}

.reject-button:hover {
    background: #dc3545;
}

.view-button {
    background: var(--tertiary-bg);
    color: var(--primary-text);
    border: 1px solid var(--quaternary-bg);
}

.view-button:hover {
    background: var(--quaternary-bg);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
    padding: var(--spacing-lg);
}

.modal-content {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--tertiary-bg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--tertiary-bg);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--tertiary-text);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--tertiary-bg);
    color: var(--primary-text);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--tertiary-bg);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.modal-form {
    padding: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--tertiary-bg);
}

/* Lists and Tables */
.permits-list,
.audits-list,
.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.list-item {
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    transition: var(--transition-normal);
}

.list-item:hover {
    background: var(--tertiary-bg);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.item-title {
    font-weight: 600;
    font-size: var(--font-size-md);
}

.item-meta {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

.item-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.item-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-field-label {
    color: var(--quaternary-text);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    font-weight: 500;
}

.item-field-value {
    font-size: var(--font-size-sm);
}

/* Activity and Recent Items */
.activity-list,
.decisions-list,
.priority-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.activity-item,
.decision-item,
.priority-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.activity-item:hover,
.decision-item:hover,
.priority-item:hover {
    background: var(--tertiary-bg);
}

.activity-icon,
.decision-icon,
.priority-icon {
    font-size: var(--font-size-lg);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tertiary-bg);
    border-radius: var(--radius-md);
}

.activity-content,
.decision-content,
.priority-content {
    flex: 1;
}

.activity-title,
.decision-title,
.priority-title {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.activity-description,
.decision-description,
.priority-description {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

.activity-time,
.decision-time,
.priority-time {
    color: var(--quaternary-text);
    font-size: var(--font-size-xs);
    white-space: nowrap;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--secondary-bg);
    border: 1px solid var(--tertiary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--accent-orange);
}

.toast.info {
    border-left: 4px solid var(--accent-blue);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    color: var(--tertiary-text);
    font-size: var(--font-size-sm);
}

.toast-close {
    background: none;
    border: none;
    color: var(--tertiary-text);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.toast-close:hover {
    background: var(--tertiary-bg);
    color: var(--primary-text);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--tertiary-text);
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-text);
}

.empty-state p {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-lg);
}

/* Responsive Design for Dashboards */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .permits-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .header-left {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .main-nav {
        justify-content: center;
        flex-wrap: wrap;
    }

    .dashboard-main {
        padding: var(--spacing-md);
    }

    .section-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .search-box {
        max-width: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .permits-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal {
        padding: var(--spacing-md);
    }

    .modal-content {
        max-width: none;
    }

    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: var(--spacing-sm);
    }

    .logo-section h1 {
        font-size: var(--font-size-md);
    }

    .nav-button {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .user-info {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .dashboard-main {
        padding: var(--spacing-sm);
    }

    .section-header h2 {
        font-size: var(--font-size-lg);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-content h3 {
        font-size: var(--font-size-xl);
    }

    .permit-card {
        padding: var(--spacing-md);
    }

    .permit-actions {
        flex-direction: column;
    }

    .modal-header,
    .modal-body,
    .modal-footer,
    .modal-form {
        padding: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    .dashboard-header,
    .section-actions,
    .permit-actions,
    .modal,
    .toast-container,
    .loading-overlay {
        display: none !important;
    }

    .dashboard-main {
        padding: 0;
        max-width: none;
    }

    .permit-card,
    .list-item {
        break-inside: avoid;
        border: 1px solid #ccc;
        margin-bottom: var(--spacing-md);
    }

    body {
        background: white !important;
        color: black !important;
    }

    .permit-card,
    .list-item,
    .stat-card {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-bg: #000000;
        --secondary-bg: #1a1a1a;
        --tertiary-bg: #333333;
        --quaternary-bg: #4d4d4d;

        --primary-text: #ffffff;
        --secondary-text: #ffffff;
        --tertiary-text: #cccccc;
        --quaternary-text: #999999;
    }

    .form-input,
    .form-select,
    .form-textarea {
        border-width: 2px;
    }

    .permit-card,
    .stat-card,
    .list-item {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-spinner,
    .spinner {
        animation: none;
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

.nav-button:focus,
.primary-button:focus,
.secondary-button:focus {
    outline-offset: 4px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--tertiary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--quaternary-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--surface-bg);
}

.signup-link {
    text-align: center;
    margin-top: 1rem;
}

.signup-link a {
    color: var(--accent-color);
    text-decoration: none;
}

.signup-link a:hover {
    text-decoration: underline;
}

