# BaseRow Database Setup for ePTW System

This document provides step-by-step instructions for setting up the BaseRow database for the electronic Permit To Work (ePTW) system.

## Prerequisites

1. BaseRow account (sign up at https://baserow.io)
2. API access enabled in your BaseRow account
3. Administrative access to create databases and tables

## Step 1: Create Database

1. Log into your BaseRow account
2. Click "Create new database"
3. Name it "ePTW_System"
4. Note down the Database ID from the URL (you'll need this for config.js)

## Step 2: Create Tables

### Table 1: Users

Create a new table named "Users" with the following fields:

| Field Name | Field Type | Settings |
|------------|------------|----------|
| id | Auto Number | Primary key (auto-created) |
| username | Single Line Text | Required, Unique |
| password_hash | Single Line Text | Required |
| role | Single Select | Options: Fireman, Electrical_Manager, Mechanical_Manager, Civil_Manager |
| department | Single Line Text | Optional (null for Fireman role) |

**Single Select Options for 'role' field:**
- Fireman
- Electrical_Manager
- Mechanical_Manager
- Civil_Manager

### Table 2: Permits

Create a new table named "Permits" with the following fields:

| Field Name | Field Type | Settings |
|------------|------------|----------|
| id | Auto Number | Primary key (auto-created) |
| work_type | Single Line Text | Required |
| location | Single Line Text | Required |
| requested_by | Single Line Text | Required |
| department | Single Select | Required, Options: Electrical, Mechanical, Civil |
| status | Single Select | Required, Default: Pending |
| image | File | Optional, Allow multiple files: No |
| created_at | Date | Auto-fill on create |
| updated_at | Date | Auto-update on modify |

**Single Select Options for 'department' field:**
- Electrical
- Mechanical
- Civil

**Single Select Options for 'status' field:**
- Pending (set as default)
- Approved
- Rejected
- Closed

### Table 3: Audits

Create a new table named "Audits" with the following fields:

| Field Name | Field Type | Settings |
|------------|------------|----------|
| id | Auto Number | Primary key (auto-created) |
| permit_id | Link to Table | Link to Permits table, Required |
| auditor_name | Single Line Text | Required |
| remarks | Long Text | Required |
| compliance_status | Single Select | Required, Options: Pass, Fail |
| audit_date | Date | Auto-fill on create |

**Single Select Options for 'compliance_status' field:**
- Pass
- Fail

## Step 3: Generate API Token

1. Go to your BaseRow account settings
2. Navigate to "API tokens" section
3. Click "Create new token"
4. Name it "ePTW_System_Token"
5. Grant the following permissions:
   - Read access to all tables
   - Create access to all tables
   - Update access to all tables
   - Delete access (optional, for maintenance)
6. Copy the generated token (you'll need this for config.js)

## Step 4: Get Table IDs

1. Navigate to each table in your BaseRow interface
2. Note the table ID from the URL (format: /database/{db_id}/table/{table_id})
3. Record the IDs for:
   - Users table
   - Permits table
   - Audits table

## Step 5: Configure Application

1. Open `config.js` in your ePTW application
2. Replace the placeholder values:
   - `YOUR_BASEROW_API_TOKEN_HERE` with your API token
   - `YOUR_DATABASE_ID_HERE` with your database ID
   - `YOUR_PERMITS_TABLE_ID_HERE` with your Permits table ID
   - `YOUR_AUDITS_TABLE_ID_HERE` with your Audits table ID
   - `YOUR_USERS_TABLE_ID_HERE` with your Users table ID

## Step 6: Create Initial Users

Add initial user records to the Users table:

### Sample Fireman User
- username: `fireman1`
- password_hash: `$2b$10$example_hash_here` (use proper bcrypt hash)
- role: `Fireman`
- department: `null` (leave empty)

### Sample Manager Users
- username: `electrical_mgr`
- password_hash: `$2b$10$example_hash_here`
- role: `Electrical_Manager`
- department: `Electrical`

- username: `mechanical_mgr`
- password_hash: `$2b$10$example_hash_here`
- role: `Mechanical_Manager`
- department: `Mechanical`

- username: `civil_mgr`
- password_hash: `$2b$10$example_hash_here`
- role: `Civil_Manager`
- department: `Civil`

## Security Considerations

1. **API Token Security**: Store API tokens securely and never commit them to version control
2. **Password Hashing**: Use proper bcrypt hashing for passwords (minimum 10 rounds)
3. **Access Control**: Ensure BaseRow workspace access is restricted to authorized personnel
4. **Regular Backups**: Set up regular database backups
5. **Token Rotation**: Regularly rotate API tokens for security

## Troubleshooting

### Common Issues

1. **API Token Invalid**: Verify token is correctly copied and has proper permissions
2. **Table ID Not Found**: Ensure table IDs are correctly extracted from BaseRow URLs
3. **Field Type Mismatch**: Verify field types match exactly as specified above
4. **Permission Denied**: Check API token permissions include required access levels

### Testing Connection

Use the browser console to test API connection:
```javascript
fetch('https://api.baserow.io/api/database/tables/{TABLE_ID}/rows/', {
    headers: {
        'Authorization': 'Token YOUR_API_TOKEN'
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

## Support

For BaseRow-specific issues, consult:
- BaseRow Documentation: https://baserow.io/docs
- BaseRow API Reference: https://api.baserow.io/api/redoc/
- BaseRow Community Forum: https://community.baserow.io/
