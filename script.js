/**
 * ePTW System - Main JavaScript File
 * Handles authentication, API communication, and dashboard functionality
 */

// Global application state
const AppState = {
    currentUser: null,
    permits: [],
    audits: [],
    isLoading: false,
    refreshInterval: null
};

// Authentication Module
const Auth = {
    /**
     * Authenticate user with BaseRow
     * @param {string} username 
     * @param {string} password 
     * @returns {Promise<Object|null>} User object or null if authentication fails
     */
    async authenticate(username, password) {
        try {
            // Hash the password for comparison (in production, use proper bcrypt)
            const passwordHash = await this.hashPassword(password);
            
            // Query BaseRow Users table
            const users = await API.getUsers();
            
            // Find matching user
            const user = users.find(u => 
                u.username === username && u.password_hash === passwordHash
            );
            
            if (user) {
                // Remove sensitive data before storing
                const safeUser = {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    department: user.department
                };
                
                return safeUser;
            }
            
            return null;
        } catch (error) {
            console.error('Authentication error:', error);
            throw new Error('Authentication failed. Please try again.');
        }
    },

    /**
     * Simple password hashing (replace with proper bcrypt in production)
     * @param {string} password 
     * @returns {Promise<string>} Hashed password
     */
    async hashPassword(password) {
        // This is a simplified hash for demo purposes
        // In production, use proper bcrypt hashing on the server side
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    },

    /**
     * Store user session
     * @param {Object} user 
     */
    storeSession(user) {
        const sessionData = {
            user: user,
            loginTime: Date.now(),
            expiresAt: Date.now() + CONFIG.APP.SESSION_TIMEOUT
        };
        
        localStorage.setItem('eptw_session', JSON.stringify(sessionData));
        AppState.currentUser = user;
    },

    /**
     * Get current session
     * @returns {Object|null} Session data or null if expired/invalid
     */
    getSession() {
        try {
            const sessionData = localStorage.getItem('eptw_session');
            if (!sessionData) return null;
            
            const session = JSON.parse(sessionData);
            
            if (session.expiresAt > Date.now()) {
                AppState.currentUser = session.user;
                return session;
            } else {
                this.clearSession();
                return null;
            }
        } catch (error) {
            this.clearSession();
            return null;
        }
    },

    /**
     * Clear user session
     */
    clearSession() {
        localStorage.removeItem('eptw_session');
        AppState.currentUser = null;
    },

    /**
     * Check if user has required role
     * @param {string} requiredRole 
     * @returns {boolean}
     */
    hasRole(requiredRole) {
        return AppState.currentUser && AppState.currentUser.role === requiredRole;
    },

    /**
     * Check if user can access department
     * @param {string} department 
     * @returns {boolean}
     */
    canAccessDepartment(department) {
        if (!AppState.currentUser) return false;
        
        // Firemen can access all departments
        if (AppState.currentUser.role === CONFIG.ROLES.FIREMAN) {
            return true;
        }
        
        // Managers can only access their department
        return AppState.currentUser.department === department;
    },

    /**
     * Register a new user
     * @param {Object} userData 
     * @returns {Promise<Object>}
     */
    registerUser: async function(userData) {
        try {
            // Check if username already exists
            const existingUsers = await API.getUsers();
            const userExists = existingUsers.find(u => u.username === userData.username);
            
            if (userExists) {
                throw new Error('Username already exists');
            }

            // Hash the password
            const passwordHash = await this.hashPassword(userData.password);

            // Create user record
            const newUser = await API.createUser({
                username: userData.username,
                password_hash: passwordHash,
                role: userData.role,
                department: userData.department
            });

            return newUser;
        } catch (error) {
            console.error('Registration error:', error);
            throw new Error('Registration failed. Please try again.');
        }
    }
};

// API Module for BaseRow communication
const API = {
    /**
     * Make authenticated request to BaseRow
     * @param {string} endpoint 
     * @param {Object} options 
     * @returns {Promise<Object>}
     */
    async request(endpoint, options = {}) {
        const url = `${CONFIG.BASEROW.API_URL}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Authorization': `Token ${CONFIG.BASEROW.API_TOKEN}`,
                'Content-Type': 'application/json'
            }
        };
        
        const requestOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    },

    /**
     * Get all users
     * @returns {Promise<Array>}
     */
    async getUsers() {
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.USERS}/rows/`);
        return response.results || [];
    },

    /**
     * Get permits with optional filtering
     * @param {Object} filters 
     * @returns {Promise<Array>}
     */
    async getPermits(filters = {}) {
        let endpoint = `/database/tables/${CONFIG.BASEROW.TABLES.PERMITS}/rows/`;
        
        // Add query parameters for filtering
        const params = new URLSearchParams();
        if (filters.department) {
            params.append('filter__department__equal', filters.department);
        }
        if (filters.status) {
            params.append('filter__status__equal', filters.status);
        }
        if (filters.search) {
            params.append('search', filters.search);
        }
        
        if (params.toString()) {
            endpoint += `?${params.toString()}`;
        }
        
        const response = await this.request(endpoint);
        return response.results || [];
    },

    /**
     * Create new permit
     * @param {Object} permitData 
     * @returns {Promise<Object>}
     */
    async createPermit(permitData) {
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.PERMITS}/rows/`, {
            method: 'POST',
            body: JSON.stringify({
                work_type: permitData.workType,
                location: permitData.location,
                requested_by: permitData.requestedBy,
                department: permitData.department,
                status: CONFIG.PERMIT_STATUS.PENDING,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
        });
        
        return response;
    },

    /**
     * Update permit status
     * @param {number} permitId 
     * @param {string} status 
     * @param {string} reason 
     * @returns {Promise<Object>}
     */
    async updatePermitStatus(permitId, status, reason = '') {
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.PERMITS}/rows/${permitId}/`, {
            method: 'PATCH',
            body: JSON.stringify({
                status: status,
                updated_at: new Date().toISOString(),
                ...(reason && { rejection_reason: reason })
            })
        });
        
        return response;
    },

    /**
     * Get audits with optional filtering
     * @param {Object} filters 
     * @returns {Promise<Array>}
     */
    async getAudits(filters = {}) {
        let endpoint = `/database/tables/${CONFIG.BASEROW.TABLES.AUDITS}/rows/`;
        
        const params = new URLSearchParams();
        if (filters.permitId) {
            params.append('filter__permit_id__equal', filters.permitId);
        }
        if (filters.complianceStatus) {
            params.append('filter__compliance_status__equal', filters.complianceStatus);
        }
        
        if (params.toString()) {
            endpoint += `?${params.toString()}`;
        }
        
        const response = await this.request(endpoint);
        return response.results || [];
    },

    /**
     * Create new audit
     * @param {Object} auditData 
     * @returns {Promise<Object>}
     */
    async createAudit(auditData) {
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.AUDITS}/rows/`, {
            method: 'POST',
            body: JSON.stringify({
                permit_id: auditData.permitId,
                auditor_name: auditData.auditorName,
                remarks: auditData.remarks,
                compliance_status: auditData.complianceStatus,
                audit_date: new Date().toISOString()
            })
        });
        
        return response;
    },

    /**
     * Upload file to BaseRow
     * @param {File} file 
     * @param {number} permitId 
     * @returns {Promise<Object>}
     */
    async uploadFile(file, permitId) {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.PERMITS}/upload-file/`, {
            method: 'POST',
            headers: {
                'Authorization': `Token ${CONFIG.BASEROW.API_TOKEN}`
                // Don't set Content-Type for FormData
            },
            body: formData
        });
        
        // Update permit with file reference
        if (response.url) {
            await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.PERMITS}/rows/${permitId}/`, {
                method: 'PATCH',
                body: JSON.stringify({
                    image: response.url
                })
            });
        }
        
        return response;
    },

    /**
     * Create a new user
     * @param {Object} userData 
     * @returns {Promise<Object>}
     */
    async createUser(userData) {
        const response = await this.request(`/database/tables/${CONFIG.BASEROW.TABLES.USERS}/rows/`, {
            method: 'POST',
            body: JSON.stringify({
                username: userData.username,
                password_hash: userData.password_hash,
                role: userData.role,
                department: userData.department
            })
        });
        
        return response;
    }
};

// Utility functions
const Utils = {
    /**
     * Format date for display
     * @param {string|Date} date 
     * @returns {string}
     */
    formatDate(date) {
        if (!date) return 'N/A';
        
        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    /**
     * Get relative time string
     * @param {string|Date} date 
     * @returns {string}
     */
    getRelativeTime(date) {
        if (!date) return 'Unknown';
        
        const now = new Date();
        const then = new Date(date);
        const diffMs = now - then;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minutes ago`;
        if (diffHours < 24) return `${diffHours} hours ago`;
        if (diffDays < 7) return `${diffDays} days ago`;
        
        return this.formatDate(date);
    },

    /**
     * Validate file upload
     * @param {File} file 
     * @returns {Object} Validation result
     */
    validateFile(file) {
        const result = { valid: true, error: null };
        
        if (!file) {
            result.valid = false;
            result.error = 'No file selected';
            return result;
        }
        
        if (file.size > CONFIG.APP.MAX_FILE_SIZE) {
            result.valid = false;
            result.error = 'File size exceeds 5MB limit';
            return result;
        }
        
        if (!CONFIG.APP.ALLOWED_FILE_TYPES.includes(file.type)) {
            result.valid = false;
            result.error = 'Invalid file type. Please use JPEG, PNG, or GIF';
            return result;
        }
        
        return result;
    },

    /**
     * Debounce function calls
     * @param {Function} func
     * @param {number} wait
     * @returns {Function}
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// UI Module for user interface interactions
const UI = {
    /**
     * Show loading overlay
     * @param {boolean} show
     * @param {string} message
     */
    showLoading(show, message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        if (!overlay) return;

        if (show) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.remove('hidden');
            AppState.isLoading = true;
        } else {
            overlay.classList.add('hidden');
            AppState.isLoading = false;
        }
    },

    /**
     * Show toast notification
     * @param {string} type - success, error, warning, info
     * @param {string} title
     * @param {string} message
     * @param {number} duration
     */
    showToast(type, title, message, duration = 5000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toast.innerHTML = `
            <div class="toast-icon">${icons[type] || icons.info}</div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">&times;</button>
        `;

        container.appendChild(toast);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, duration);
        }
    },

    /**
     * Show/hide modal
     * @param {string} modalId
     * @param {boolean} show
     */
    showModal(modalId, show) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        if (show) {
            modal.classList.remove('hidden');
            // Focus first input
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        } else {
            modal.classList.add('hidden');
        }
    },

    /**
     * Set button loading state
     * @param {HTMLElement} button
     * @param {boolean} loading
     */
    setButtonLoading(button, loading) {
        if (!button) return;

        const text = button.querySelector('.button-text');
        const spinner = button.querySelector('.button-spinner');

        if (loading) {
            button.disabled = true;
            if (text) text.classList.add('hidden');
            if (spinner) spinner.classList.remove('hidden');
        } else {
            button.disabled = false;
            if (text) text.classList.remove('hidden');
            if (spinner) spinner.classList.add('hidden');
        }
    },

    /**
     * Update navigation active state
     * @param {string} activeSection
     */
    updateNavigation(activeSection) {
        // Update nav buttons
        document.querySelectorAll('.nav-button').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.section === activeSection) {
                btn.classList.add('active');
            }
        });

        // Update sections
        document.querySelectorAll('.dashboard-section').forEach(section => {
            section.classList.remove('active');
            if (section.id === `${activeSection}Section`) {
                section.classList.add('active');
            }
        });
    },

    /**
     * Render empty state
     * @param {HTMLElement} container
     * @param {string} icon
     * @param {string} title
     * @param {string} message
     */
    renderEmptyState(container, icon, title, message) {
        if (!container) return;

        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">${icon}</div>
                <h3>${title}</h3>
                <p>${message}</p>
            </div>
        `;
    },

    /**
     * Clear form inputs
     * @param {HTMLFormElement} form
     */
    clearForm(form) {
        if (!form) return;

        form.querySelectorAll('input, select, textarea').forEach(input => {
            if (input.type === 'file') {
                input.value = '';
            } else if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });
    },

    /**
     * Validate form inputs
     * @param {HTMLFormElement} form
     * @returns {Object} Validation result
     */
    validateForm(form) {
        const result = { valid: true, errors: [] };

        if (!form) {
            result.valid = false;
            result.errors.push('Form not found');
            return result;
        }

        const requiredInputs = form.querySelectorAll('[required]');

        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                result.valid = false;
                result.errors.push(`${input.name || input.id} is required`);
                input.classList.add('error');
            } else {
                input.classList.remove('error');
            }
        });

        return result;
    }
};

// Event handlers for common UI interactions
const EventHandlers = {
    /**
     * Initialize common event listeners
     */
    init() {
        // Navigation handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-button')) {
                const section = e.target.dataset.section;
                if (section) {
                    UI.updateNavigation(section);
                }
            }
        });

        // Modal close handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('.modal-close') || e.target.matches('.modal')) {
                const modal = e.target.closest('.modal');
                if (modal && e.target === modal) {
                    modal.classList.add('hidden');
                }
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal:not(.hidden)').forEach(modal => {
                    modal.classList.add('hidden');
                });
            }
        });

        // Logout handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('#logoutButton') || e.target.closest('#logoutButton')) {
                this.handleLogout();
            }
        });

        // Refresh handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('#refreshButton') || e.target.closest('#refreshButton')) {
                this.handleRefresh();
            }
        });
    },

    /**
     * Handle user logout
     */
    handleLogout() {
        Auth.clearSession();

        // Clear any intervals
        if (AppState.refreshInterval) {
            clearInterval(AppState.refreshInterval);
        }

        // Redirect to login
        window.location.href = 'index.html';
    },

    /**
     * Handle data refresh
     */
    async handleRefresh() {
        try {
            UI.showLoading(true, 'Refreshing data...');

            // Refresh data based on current page
            if (window.location.pathname.includes('fireman-dashboard')) {
                await FiremanDashboard.loadData();
            } else if (window.location.pathname.includes('manager-dashboard')) {
                await ManagerDashboard.loadData();
            }

            UI.showToast('success', 'Refreshed', 'Data updated successfully');
        } catch (error) {
            console.error('Refresh error:', error);
            UI.showToast('error', 'Refresh Failed', 'Could not refresh data');
        } finally {
            UI.showLoading(false);
        }
    }
};

// Dashboard initialization functions
function initializeFiremanDashboard() {
    // Check authentication
    const session = Auth.getSession();
    if (!session || !Auth.hasRole(CONFIG.ROLES.FIREMAN)) {
        window.location.href = 'index.html';
        return;
    }

    // Initialize UI
    setupFiremanDashboard();

    // Load initial data
    FiremanDashboard.loadData();

    // Setup auto-refresh
    AppState.refreshInterval = setInterval(() => {
        FiremanDashboard.loadData();
    }, CONFIG.APP.REFRESH_INTERVAL);
}

function initializeManagerDashboard() {
    // Check authentication
    const session = Auth.getSession();
    if (!session || !session.user.role.includes('Manager')) {
        window.location.href = 'index.html';
        return;
    }

    // Initialize UI
    setupManagerDashboard();

    // Load initial data
    ManagerDashboard.loadData();

    // Setup auto-refresh
    AppState.refreshInterval = setInterval(() => {
        ManagerDashboard.loadData();
    }, CONFIG.APP.REFRESH_INTERVAL);
}

function setupFiremanDashboard() {
    // Initialize common event handlers
    EventHandlers.init();

    // Set user info
    const userNameEl = document.getElementById('userName');
    if (userNameEl && AppState.currentUser) {
        userNameEl.textContent = AppState.currentUser.username;
    }

    // Setup form handlers
    setupPermitForm();
    setupAuditForm();

    // Setup search and filter handlers
    setupSearchAndFilter();
}

function setupManagerDashboard() {
    // Initialize common event handlers
    EventHandlers.init();

    // Set user info and department
    updateManagerInfo();

    // Setup permit action handlers
    setupPermitActions();

    // Setup search and filter handlers
    setupSearchAndFilter();
}

function updateManagerInfo() {
    if (!AppState.currentUser) return;

    const userNameEl = document.getElementById('userName');
    const deptNameEl = document.getElementById('deptName');
    const deptIconEl = document.getElementById('deptIcon');
    const userRoleTextEl = document.getElementById('userRoleText');

    if (userNameEl) userNameEl.textContent = AppState.currentUser.username;
    if (deptNameEl) deptNameEl.textContent = AppState.currentUser.department;
    if (userRoleTextEl) userRoleTextEl.textContent = `${AppState.currentUser.department} Manager`;

    // Set department icon
    if (deptIconEl) {
        const icons = {
            'Electrical': '⚡',
            'Mechanical': '⚙️',
            'Civil': '🏗️'
        };
        deptIconEl.textContent = icons[AppState.currentUser.department] || '👨‍💼';
    }
}

function setupPermitForm() {
    const createPermitBtn = document.getElementById('createPermitBtn');
    const newPermitBtn = document.getElementById('newPermitBtn');
    const createPermitModal = document.getElementById('createPermitModal');
    const createPermitForm = document.getElementById('createPermitForm');
    const closePermitModal = document.getElementById('closePermitModal');
    const cancelPermit = document.getElementById('cancelPermit');

    // Open modal handlers
    [createPermitBtn, newPermitBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', () => {
                UI.showModal('createPermitModal', true);
            });
        }
    });

    // Close modal handlers
    [closePermitModal, cancelPermit].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', () => {
                UI.showModal('createPermitModal', false);
                UI.clearForm(createPermitForm);
            });
        }
    });

    // Form submission
    if (createPermitForm) {
        createPermitForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await FiremanDashboard.createPermit(createPermitForm);
        });
    }
}

function setupAuditForm() {
    const createAuditBtn = document.getElementById('createAuditBtn');
    const newAuditBtn = document.getElementById('newAuditBtn');
    const createAuditModal = document.getElementById('createAuditModal');
    const createAuditForm = document.getElementById('createAuditForm');
    const closeAuditModal = document.getElementById('closeAuditModal');
    const cancelAudit = document.getElementById('cancelAudit');

    // Open modal handlers
    [createAuditBtn, newAuditBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', async () => {
                await populatePermitDropdown();
                UI.showModal('createAuditModal', true);
            });
        }
    });

    // Close modal handlers
    [closeAuditModal, cancelAudit].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', () => {
                UI.showModal('createAuditModal', false);
                UI.clearForm(createAuditForm);
            });
        }
    });

    // Form submission
    if (createAuditForm) {
        createAuditForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await FiremanDashboard.createAudit(createAuditForm);
        });
    }
}

async function populatePermitDropdown() {
    const permitSelect = document.getElementById('permitId');
    if (!permitSelect) return;

    try {
        const permits = await API.getPermits({ status: CONFIG.PERMIT_STATUS.APPROVED });

        permitSelect.innerHTML = '<option value="">Select Permit to Audit</option>';

        permits.forEach(permit => {
            const option = document.createElement('option');
            option.value = permit.id;
            option.textContent = `#${permit.id} - ${permit.work_type} (${permit.location})`;
            permitSelect.appendChild(option);
        });

        if (permits.length === 0) {
            permitSelect.innerHTML = '<option value="">No approved permits available</option>';
        }
    } catch (error) {
        console.error('Error loading permits:', error);
        permitSelect.innerHTML = '<option value="">Error loading permits</option>';
    }
}

function setupPermitActions() {
    // Permit detail modal handlers will be set up when permits are rendered
    const permitDetailModal = document.getElementById('permitDetailModal');
    const approvalModal = document.getElementById('approvalModal');
    const rejectionModal = document.getElementById('rejectionModal');

    // Close modal handlers
    document.addEventListener('click', (e) => {
        if (e.target.matches('#closeDetailModal')) {
            UI.showModal('permitDetailModal', false);
        }
        if (e.target.matches('#closeApprovalModal') || e.target.matches('#cancelApproval')) {
            UI.showModal('approvalModal', false);
        }
        if (e.target.matches('#closeRejectionModal') || e.target.matches('#cancelRejection')) {
            UI.showModal('rejectionModal', false);
        }
    });

    // Approval confirmation
    const confirmApproval = document.getElementById('confirmApproval');
    if (confirmApproval) {
        confirmApproval.addEventListener('click', async () => {
            await ManagerDashboard.approvePermit();
        });
    }

    // Rejection form submission
    const rejectionForm = document.getElementById('rejectionForm');
    if (rejectionForm) {
        rejectionForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await ManagerDashboard.rejectPermit(rejectionForm);
        });
    }
}

function setupSearchAndFilter() {
    // Search functionality
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        if (input) {
            input.addEventListener('input', Utils.debounce((e) => {
                const searchTerm = e.target.value.toLowerCase();
                filterContent(searchTerm, input.closest('.dashboard-section'));
            }, 300));
        }
    });

    // Filter functionality
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        if (select) {
            select.addEventListener('change', (e) => {
                const filterValue = e.target.value;
                applyFilter(filterValue, select);
            });
        }
    });
}

function filterContent(searchTerm, section) {
    if (!section) return;

    const items = section.querySelectorAll('.permit-card, .list-item, .activity-item');

    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

function applyFilter(filterValue, selectElement) {
    // Filter implementation will depend on the specific filter type
    // This is a placeholder for the actual filter logic
    console.log('Applying filter:', filterValue, selectElement.id);
}

// Fireman Dashboard Module
const FiremanDashboard = {
    /**
     * Load all dashboard data
     */
    async loadData() {
        try {
            UI.showLoading(true, 'Loading dashboard data...');

            // Load permits and audits
            const [permits, audits] = await Promise.all([
                API.getPermits(),
                API.getAudits()
            ]);

            AppState.permits = permits;
            AppState.audits = audits;

            // Update UI
            this.updateStatistics();
            this.renderPermits();
            this.renderAudits();
            this.renderRecentActivity();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            UI.showToast('error', 'Load Error', 'Failed to load dashboard data');
        } finally {
            UI.showLoading(false);
        }
    },

    /**
     * Update statistics cards
     */
    updateStatistics() {
        const totalPermitsEl = document.getElementById('totalPermits');
        const pendingPermitsEl = document.getElementById('pendingPermits');
        const approvedPermitsEl = document.getElementById('approvedPermits');
        const totalAuditsEl = document.getElementById('totalAudits');

        if (totalPermitsEl) totalPermitsEl.textContent = AppState.permits.length;
        if (pendingPermitsEl) {
            const pending = AppState.permits.filter(p => p.status === CONFIG.PERMIT_STATUS.PENDING).length;
            pendingPermitsEl.textContent = pending;
        }
        if (approvedPermitsEl) {
            const approved = AppState.permits.filter(p => p.status === CONFIG.PERMIT_STATUS.APPROVED).length;
            approvedPermitsEl.textContent = approved;
        }
        if (totalAuditsEl) totalAuditsEl.textContent = AppState.audits.length;
    },

    /**
     * Render permits list
     */
    renderPermits() {
        const container = document.getElementById('permitsList');
        if (!container) return;

        if (AppState.permits.length === 0) {
            UI.renderEmptyState(container, '📋', 'No Permits Found', 'Create your first permit to get started');
            return;
        }

        container.innerHTML = AppState.permits.map(permit => `
            <div class="list-item permit-item" data-permit-id="${permit.id}">
                <div class="item-header">
                    <div class="item-title">${permit.work_type}</div>
                    <div class="permit-status ${permit.status.toLowerCase()}">${permit.status}</div>
                </div>
                <div class="item-content">
                    <div class="item-field">
                        <div class="item-field-label">Location</div>
                        <div class="item-field-value">${permit.location}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Requested By</div>
                        <div class="item-field-value">${permit.requested_by}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Department</div>
                        <div class="item-field-value">${permit.department}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Created</div>
                        <div class="item-field-value">${Utils.formatDate(permit.created_at)}</div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * Render audits list
     */
    renderAudits() {
        const container = document.getElementById('auditsList');
        if (!container) return;

        if (AppState.audits.length === 0) {
            UI.renderEmptyState(container, '🔍', 'No Audits Found', 'Conduct your first audit to ensure safety compliance');
            return;
        }

        container.innerHTML = AppState.audits.map(audit => `
            <div class="list-item audit-item" data-audit-id="${audit.id}">
                <div class="item-header">
                    <div class="item-title">Audit #${audit.id}</div>
                    <div class="permit-status ${audit.compliance_status.toLowerCase()}">${audit.compliance_status}</div>
                </div>
                <div class="item-content">
                    <div class="item-field">
                        <div class="item-field-label">Permit ID</div>
                        <div class="item-field-value">#${audit.permit_id}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Auditor</div>
                        <div class="item-field-value">${audit.auditor_name}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Date</div>
                        <div class="item-field-value">${Utils.formatDate(audit.audit_date)}</div>
                    </div>
                    <div class="item-field">
                        <div class="item-field-label">Remarks</div>
                        <div class="item-field-value">${audit.remarks.substring(0, 100)}${audit.remarks.length > 100 ? '...' : ''}</div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * Render recent activity
     */
    renderRecentActivity() {
        const container = document.getElementById('recentActivityList');
        if (!container) return;

        // Combine permits and audits for recent activity
        const activities = [];

        // Add recent permits
        AppState.permits.slice(0, 5).forEach(permit => {
            activities.push({
                type: 'permit',
                icon: '📋',
                title: `Permit Created: ${permit.work_type}`,
                description: `${permit.department} department - ${permit.location}`,
                time: permit.created_at,
                status: permit.status
            });
        });

        // Add recent audits
        AppState.audits.slice(0, 5).forEach(audit => {
            activities.push({
                type: 'audit',
                icon: '🔍',
                title: `Audit Completed: ${audit.compliance_status}`,
                description: `Permit #${audit.permit_id} - ${audit.auditor_name}`,
                time: audit.audit_date,
                status: audit.compliance_status
            });
        });

        // Sort by time and take most recent
        activities.sort((a, b) => new Date(b.time) - new Date(a.time));
        const recentActivities = activities.slice(0, 10);

        if (recentActivities.length === 0) {
            UI.renderEmptyState(container, '📊', 'No Recent Activity', 'Activity will appear here as you create permits and audits');
            return;
        }

        container.innerHTML = recentActivities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">${activity.icon}</div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                </div>
                <div class="activity-time">${Utils.getRelativeTime(activity.time)}</div>
            </div>
        `).join('');
    },

    /**
     * Create new permit
     * @param {HTMLFormElement} form
     */
    async createPermit(form) {
        try {
            const validation = UI.validateForm(form);
            if (!validation.valid) {
                UI.showToast('error', 'Validation Error', validation.errors.join(', '));
                return;
            }

            const submitBtn = form.querySelector('#submitPermit');
            UI.setButtonLoading(submitBtn, true);

            const formData = new FormData(form);
            const permitData = {
                workType: formData.get('workType'),
                location: formData.get('location'),
                requestedBy: formData.get('requestedBy'),
                department: formData.get('department')
            };

            // Create permit
            const newPermit = await API.createPermit(permitData);

            // Handle file upload if present
            const imageFile = formData.get('permitImage');
            if (imageFile && imageFile.size > 0) {
                const fileValidation = Utils.validateFile(imageFile);
                if (fileValidation.valid) {
                    await API.uploadFile(imageFile, newPermit.id);
                } else {
                    UI.showToast('warning', 'File Upload Warning', fileValidation.error);
                }
            }

            // Success
            UI.showToast('success', 'Permit Created', 'New permit has been created successfully');
            UI.showModal('createPermitModal', false);
            UI.clearForm(form);

            // Reload data
            await this.loadData();

        } catch (error) {
            console.error('Error creating permit:', error);
            UI.showToast('error', 'Creation Failed', 'Could not create permit. Please try again.');
        } finally {
            const submitBtn = form.querySelector('#submitPermit');
            UI.setButtonLoading(submitBtn, false);
        }
    },

    /**
     * Create new audit
     * @param {HTMLFormElement} form
     */
    async createAudit(form) {
        try {
            const validation = UI.validateForm(form);
            if (!validation.valid) {
                UI.showToast('error', 'Validation Error', validation.errors.join(', '));
                return;
            }

            const submitBtn = form.querySelector('#submitAudit');
            UI.setButtonLoading(submitBtn, true);

            const formData = new FormData(form);
            const auditData = {
                permitId: parseInt(formData.get('permitId')),
                auditorName: formData.get('auditorName'),
                remarks: formData.get('remarks'),
                complianceStatus: formData.get('complianceStatus')
            };

            // Create audit
            await API.createAudit(auditData);

            // Success
            UI.showToast('success', 'Audit Created', 'Safety audit has been recorded successfully');
            UI.showModal('createAuditModal', false);
            UI.clearForm(form);

            // Reload data
            await this.loadData();

        } catch (error) {
            console.error('Error creating audit:', error);
            UI.showToast('error', 'Creation Failed', 'Could not create audit. Please try again.');
        } finally {
            const submitBtn = form.querySelector('#submitAudit');
            UI.setButtonLoading(submitBtn, false);
        }
    }
};

// Manager Dashboard Module
const ManagerDashboard = {
    currentPermit: null,

    /**
     * Load dashboard data for manager
     */
    async loadData() {
        try {
            UI.showLoading(true, 'Loading department data...');

            // Get permits for manager's department only
            const permits = await API.getPermits({
                department: AppState.currentUser.department
            });

            AppState.permits = permits;

            // Update UI
            this.updateStatistics();
            this.renderPermits();
            this.renderPriorityPermits();
            this.renderRecentDecisions();

        } catch (error) {
            console.error('Error loading manager dashboard:', error);
            UI.showToast('error', 'Load Error', 'Failed to load department data');
        } finally {
            UI.showLoading(false);
        }
    },

    /**
     * Update statistics for manager dashboard
     */
    updateStatistics() {
        const pendingCountEl = document.getElementById('pendingCount');
        const approvedCountEl = document.getElementById('approvedCount');
        const rejectedCountEl = document.getElementById('rejectedCount');
        const totalCountEl = document.getElementById('totalCount');

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const pending = AppState.permits.filter(p => p.status === CONFIG.PERMIT_STATUS.PENDING).length;
        const approvedToday = AppState.permits.filter(p =>
            p.status === CONFIG.PERMIT_STATUS.APPROVED &&
            new Date(p.updated_at) >= today
        ).length;
        const rejectedToday = AppState.permits.filter(p =>
            p.status === CONFIG.PERMIT_STATUS.REJECTED &&
            new Date(p.updated_at) >= today
        ).length;

        // This month
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);
        const totalThisMonth = AppState.permits.filter(p =>
            new Date(p.created_at) >= thisMonth
        ).length;

        if (pendingCountEl) pendingCountEl.textContent = pending;
        if (approvedCountEl) approvedCountEl.textContent = approvedToday;
        if (rejectedCountEl) rejectedCountEl.textContent = rejectedToday;
        if (totalCountEl) totalCountEl.textContent = totalThisMonth;
    },

    /**
     * Render permits in card format
     */
    renderPermits() {
        const container = document.getElementById('permitsGrid');
        if (!container) return;

        if (AppState.permits.length === 0) {
            UI.renderEmptyState(container, '📋', 'No Permits Found', 'No permits for your department yet');
            return;
        }

        container.innerHTML = AppState.permits.map(permit => `
            <div class="permit-card" data-permit-id="${permit.id}">
                <div class="permit-header">
                    <div>
                        <div class="permit-title">${permit.work_type}</div>
                        <div class="permit-id">#${permit.id}</div>
                    </div>
                    <div class="permit-status ${permit.status.toLowerCase()}">${permit.status}</div>
                </div>
                <div class="permit-details">
                    <div class="permit-detail">
                        <span class="permit-detail-label">Location:</span>
                        <span class="permit-detail-value">${permit.location}</span>
                    </div>
                    <div class="permit-detail">
                        <span class="permit-detail-label">Requested by:</span>
                        <span class="permit-detail-value">${permit.requested_by}</span>
                    </div>
                    <div class="permit-detail">
                        <span class="permit-detail-label">Created:</span>
                        <span class="permit-detail-value">${Utils.formatDate(permit.created_at)}</span>
                    </div>
                </div>
                ${permit.image ? `<img src="${permit.image}" alt="Permit Image" class="permit-image">` : ''}
                <div class="permit-actions">
                    ${permit.status === CONFIG.PERMIT_STATUS.PENDING ? `
                        <button class="permit-action-btn reject-button" onclick="ManagerDashboard.showRejectModal(${permit.id})">
                            <span class="button-icon">❌</span>
                            Reject
                        </button>
                        <button class="permit-action-btn approve-button" onclick="ManagerDashboard.showApproveModal(${permit.id})">
                            <span class="button-icon">✅</span>
                            Approve
                        </button>
                    ` : `
                        <button class="permit-action-btn view-button" onclick="ManagerDashboard.showPermitDetail(${permit.id})">
                            <span class="button-icon">👁️</span>
                            View Details
                        </button>
                    `}
                </div>
            </div>
        `).join('');
    },

    /**
     * Render priority permits requiring attention
     */
    renderPriorityPermits() {
        const container = document.getElementById('priorityPermits');
        if (!container) return;

        // Get pending permits older than 24 hours
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const priorityPermits = AppState.permits.filter(p =>
            p.status === CONFIG.PERMIT_STATUS.PENDING &&
            new Date(p.created_at) < oneDayAgo
        );

        if (priorityPermits.length === 0) {
            container.innerHTML = `
                <div class="priority-item">
                    <div class="priority-icon">✅</div>
                    <div class="priority-content">
                        <div class="priority-title">All caught up!</div>
                        <div class="priority-description">No permits require immediate attention</div>
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = priorityPermits.map(permit => `
            <div class="priority-item" onclick="ManagerDashboard.showPermitDetail(${permit.id})">
                <div class="priority-icon">⚠️</div>
                <div class="priority-content">
                    <div class="priority-title">${permit.work_type}</div>
                    <div class="priority-description">Pending for ${Utils.getRelativeTime(permit.created_at)}</div>
                </div>
                <div class="priority-time">${permit.location}</div>
            </div>
        `).join('');
    },

    /**
     * Render recent decisions
     */
    renderRecentDecisions() {
        const container = document.getElementById('recentDecisions');
        if (!container) return;

        // Get recently approved/rejected permits
        const recentDecisions = AppState.permits
            .filter(p => p.status !== CONFIG.PERMIT_STATUS.PENDING)
            .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
            .slice(0, 5);

        if (recentDecisions.length === 0) {
            UI.renderEmptyState(container, '📝', 'No Recent Decisions', 'Your permit decisions will appear here');
            return;
        }

        container.innerHTML = recentDecisions.map(permit => `
            <div class="decision-item">
                <div class="decision-icon">${permit.status === CONFIG.PERMIT_STATUS.APPROVED ? '✅' : '❌'}</div>
                <div class="decision-content">
                    <div class="decision-title">${permit.status}: ${permit.work_type}</div>
                    <div class="decision-description">${permit.location} - ${permit.requested_by}</div>
                </div>
                <div class="decision-time">${Utils.getRelativeTime(permit.updated_at)}</div>
            </div>
        `).join('');
    },

    /**
     * Show permit detail modal
     * @param {number} permitId
     */
    showPermitDetail(permitId) {
        const permit = AppState.permits.find(p => p.id === permitId);
        if (!permit) return;

        this.currentPermit = permit;

        const modalBody = document.querySelector('#permitDetailModal .permit-detail-content');
        if (!modalBody) return;

        modalBody.innerHTML = `
            <div class="permit-detail-header">
                <h4>Permit #${permit.id}</h4>
                <div class="permit-status ${permit.status.toLowerCase()}">${permit.status}</div>
            </div>
            <div class="permit-detail-grid">
                <div class="detail-field">
                    <label>Work Type</label>
                    <div>${permit.work_type}</div>
                </div>
                <div class="detail-field">
                    <label>Location</label>
                    <div>${permit.location}</div>
                </div>
                <div class="detail-field">
                    <label>Requested By</label>
                    <div>${permit.requested_by}</div>
                </div>
                <div class="detail-field">
                    <label>Department</label>
                    <div>${permit.department}</div>
                </div>
                <div class="detail-field">
                    <label>Created</label>
                    <div>${Utils.formatDate(permit.created_at)}</div>
                </div>
                <div class="detail-field">
                    <label>Last Updated</label>
                    <div>${Utils.formatDate(permit.updated_at)}</div>
                </div>
            </div>
            ${permit.image ? `
                <div class="detail-field">
                    <label>Attached Image</label>
                    <img src="${permit.image}" alt="Permit Image" style="max-width: 100%; border-radius: 8px;">
                </div>
            ` : ''}
        `;

        // Show/hide action buttons based on status
        const approveBtn = document.getElementById('approvePermitBtn');
        const rejectBtn = document.getElementById('rejectPermitBtn');

        if (permit.status === CONFIG.PERMIT_STATUS.PENDING) {
            if (approveBtn) approveBtn.style.display = 'inline-flex';
            if (rejectBtn) rejectBtn.style.display = 'inline-flex';
        } else {
            if (approveBtn) approveBtn.style.display = 'none';
            if (rejectBtn) rejectBtn.style.display = 'none';
        }

        UI.showModal('permitDetailModal', true);
    },

    /**
     * Show approve confirmation modal
     * @param {number} permitId
     */
    showApproveModal(permitId) {
        const permit = AppState.permits.find(p => p.id === permitId);
        if (!permit) return;

        this.currentPermit = permit;

        const summaryEl = document.getElementById('approvalPermitSummary');
        if (summaryEl) {
            summaryEl.innerHTML = `
                <div><strong>Work Type:</strong> ${permit.work_type}</div>
                <div><strong>Location:</strong> ${permit.location}</div>
                <div><strong>Requested by:</strong> ${permit.requested_by}</div>
            `;
        }

        UI.showModal('approvalModal', true);
    },

    /**
     * Show reject reason modal
     * @param {number} permitId
     */
    showRejectModal(permitId) {
        const permit = AppState.permits.find(p => p.id === permitId);
        if (!permit) return;

        this.currentPermit = permit;
        UI.showModal('rejectionModal', true);
    },

    /**
     * Approve current permit
     */
    async approvePermit() {
        if (!this.currentPermit) return;

        try {
            const confirmBtn = document.getElementById('confirmApproval');
            UI.setButtonLoading(confirmBtn, true);

            await API.updatePermitStatus(this.currentPermit.id, CONFIG.PERMIT_STATUS.APPROVED);

            UI.showToast('success', 'Permit Approved', 'Permit has been approved successfully');
            UI.showModal('approvalModal', false);
            UI.showModal('permitDetailModal', false);

            // Reload data
            await this.loadData();

        } catch (error) {
            console.error('Error approving permit:', error);
            UI.showToast('error', 'Approval Failed', 'Could not approve permit. Please try again.');
        } finally {
            const confirmBtn = document.getElementById('confirmApproval');
            UI.setButtonLoading(confirmBtn, false);
        }
    },

    /**
     * Reject current permit with reason
     * @param {HTMLFormElement} form
     */
    async rejectPermit(form) {
  
      if (!this.currentPermit) return;

        try {
            const validation = UI.validateForm(form);
            if (!validation.valid) {
                UI.showToast('error', 'Validation Error', validation.errors.join(', '));
                return;
            }

            const confirmBtn = document.getElementById('confirmRejection');
            UI.setButtonLoading(confirmBtn, true);

            const formData = new FormData(form);
            const reason = formData.get('rejectionReason');

            await API.updatePermitStatus(this.currentPermit.id, CONFIG.PERMIT_STATUS.REJECTED, reason);

            UI.showToast('success', 'Permit Rejected', 'Permit has been rejected with reason');
            UI.showModal('rejectionModal', false);
            UI.showModal('permitDetailModal', false);
            UI.clearForm(form);

            // Reload data
            await this.loadData();

        } catch (error) {
            console.error('Error rejecting permit:', error);
            UI.showToast('error', 'Rejection Failed', 'Could not reject permit. Please try again.');
        } finally {
            const confirmBtn = document.getElementById('confirmRejection');
            UI.setButtonLoading(confirmBtn, false);
        }
    }
};



