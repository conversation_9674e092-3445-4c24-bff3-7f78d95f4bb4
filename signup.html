<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - ePTW System</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <h1>Create Account</h1>
            <form id="signupForm" class="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" name="role" required>
                        <option value="">Select Role</option>
                        <option value="Fireman">Fireman</option>
                        <option value="Electrical_Manager">Electrical Manager</option>
                        <option value="Mechanical_Manager">Mechanical Manager</option>
                        <option value="Civil_Manager">Civil Manager</option>
                    </select>
                </div>
                <div class="form-group" id="departmentGroup" style="display: none;">
                    <label for="department">Department</label>
                    <select id="department" name="department">
                        <option value="">Select Department</option>
                        <option value="Electrical">Electrical</option>
                        <option value="Mechanical">Mechanical</option>
                        <option value="Civil">Civil</option>
                    </select>
                </div>
                <button type="submit" class="login-button">Create Account</button>
            </form>
            <p><a href="index.html">Already have an account? Sign in</a></p>
        </div>
    </div>
    <script src="config.js"></script>
    <script src="script.js"></script>
    <script>
        // Show/hide department field based on role
        document.getElementById('role').addEventListener('change', function() {
            const departmentGroup = document.getElementById('departmentGroup');
            const department = document.getElementById('department');
            
            if (this.value.includes('Manager')) {
                departmentGroup.style.display = 'block';
                department.required = true;
            } else {
                departmentGroup.style.display = 'none';
                department.required = false;
                department.value = '';
            }
        });

        // Handle form submission
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await handleSignup(e);
        });

        async function handleSignup(e) {
            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword'),
                role: formData.get('role'),
                department: formData.get('department') || null
            };

            // Validate passwords match
            if (userData.password !== userData.confirmPassword) {
                alert('Passwords do not match');
                return;
            }

            try {
                await Auth.registerUser(userData);
                alert('Account created successfully! Please sign in.');
                window.location.href = 'index.html';
            } catch (error) {
                alert('Registration failed: ' + error.message);
            }
        }
    </script>
</body>
</html>