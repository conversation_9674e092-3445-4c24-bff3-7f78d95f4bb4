# ePTW System Deployment Guide

This guide provides comprehensive instructions for deploying the electronic Permit To Work (ePTW) system in an industrial environment.

## Prerequisites

### System Requirements
- **Web Server**: Apache, Nginx, or IIS
- **HTTPS Support**: SSL certificate for secure communication
- **Modern Browsers**: Chrome 70+, Firefox 65+, Safari 12+, Edge 79+
- **Network**: Stable internet connection for BaseRow API access
- **Storage**: Minimum 100MB for application files

### BaseRow Account Setup
1. Create BaseRow account at https://baserow.io
2. Subscribe to appropriate plan (Pro recommended for production)
3. Generate API token with full permissions
4. Note your workspace and database IDs

## Installation Steps

### 1. Download and Extract Files
```bash
# Extract ePTW system files to web server directory
# Example for Apache on Linux:
sudo cp -r ePTW/* /var/www/html/eptw/
sudo chown -R www-data:www-data /var/www/html/eptw/
sudo chmod -R 755 /var/www/html/eptw/
```

### 2. Configure BaseRow Database
Follow the detailed instructions in `BASEROW_SETUP.md`:
1. Create database and tables
2. Configure field types and relationships
3. Generate API token
4. Note table IDs

### 3. Configure Application
Edit `config.js` with your BaseRow credentials:

```javascript
const CONFIG = {
    BASEROW: {
        API_URL: 'https://api.baserow.io/api',
        API_TOKEN: 'your_actual_api_token_here',
        DATABASE_ID: 'your_database_id_here',
        TABLES: {
            PERMITS: 'your_permits_table_id',
            AUDITS: 'your_audits_table_id',
            USERS: 'your_users_table_id'
        }
    }
    // ... rest of configuration
};
```

### 4. Create Initial Users
Add user records to BaseRow Users table:

```sql
-- Example users (add via BaseRow interface)
Username: fireman1
Password Hash: [bcrypt hash of password]
Role: Fireman
Department: null

Username: electrical_mgr
Password Hash: [bcrypt hash of password]
Role: Electrical_Manager
Department: Electrical
```

### 5. Web Server Configuration

#### Apache Configuration
Create `.htaccess` file in application root:
```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"

# Cache control
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /var/www/html/eptw;
    index index.html;
    
    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
}
```

## Security Configuration

### 1. HTTPS Setup
- **Required**: Deploy only with HTTPS enabled
- **Certificate**: Use valid SSL certificate (Let's Encrypt recommended)
- **HSTS**: Enable HTTP Strict Transport Security
- **Redirect**: Force HTTP to HTTPS redirect

### 2. API Security
```javascript
// In config.js - Production security settings
const CONFIG = {
    APP: {
        // Reduce session timeout for high-security environments
        SESSION_TIMEOUT: 8 * 60 * 60 * 1000, // 8 hours
        
        // Enable additional security features
        REQUIRE_HTTPS: true,
        ENABLE_CSP: true,
        LOG_SECURITY_EVENTS: true
    }
};
```

### 3. Content Security Policy
Add to HTML head sections:
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https://api.baserow.io;
    connect-src 'self' https://api.baserow.io;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
">
```

### 4. Network Security
- **Firewall**: Configure firewall to allow only necessary ports (80, 443)
- **VPN**: Consider VPN access for additional security
- **IP Whitelist**: Restrict access to known IP ranges if possible
- **Rate Limiting**: Implement rate limiting on web server

## Environment-Specific Configurations

### Development Environment
```javascript
// config.js - Development settings
const CONFIG = {
    APP: {
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
        REFRESH_INTERVAL: 10000, // 10 seconds
        DEBUG_MODE: true
    }
};
```

### Production Environment
```javascript
// config.js - Production settings
const CONFIG = {
    APP: {
        SESSION_TIMEOUT: 8 * 60 * 60 * 1000, // 8 hours
        REFRESH_INTERVAL: 30000, // 30 seconds
        DEBUG_MODE: false
    }
};
```

### High-Security Environment
```javascript
// config.js - High-security settings
const CONFIG = {
    APP: {
        SESSION_TIMEOUT: 4 * 60 * 60 * 1000, // 4 hours
        REFRESH_INTERVAL: 60000, // 1 minute
        MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
        REQUIRE_2FA: true // Future enhancement
    }
};
```

## Monitoring and Maintenance

### 1. Health Checks
Create monitoring script:
```bash
#!/bin/bash
# health-check.sh
curl -f https://your-domain.com/eptw/ > /dev/null
if [ $? -ne 0 ]; then
    echo "ePTW system is down" | mail -s "ePTW Alert" <EMAIL>
fi
```

### 2. Log Monitoring
Monitor web server logs for:
- Failed login attempts
- API errors
- Unusual traffic patterns
- File upload failures

### 3. Backup Strategy
```bash
#!/bin/bash
# backup-script.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/eptw"

# Backup application files
tar -czf "$BACKUP_DIR/eptw_files_$DATE.tar.gz" /var/www/html/eptw/

# Export BaseRow data (manual process via BaseRow interface)
echo "Remember to export BaseRow data manually"
```

### 4. Update Procedures
1. **Test Updates**: Always test in staging environment first
2. **Backup**: Create full backup before updates
3. **Maintenance Window**: Schedule updates during low-usage periods
4. **Rollback Plan**: Prepare rollback procedures

## Troubleshooting

### Common Issues

#### 1. API Connection Failed
**Symptoms**: Login fails, data doesn't load
**Solutions**:
- Verify API token validity
- Check BaseRow service status
- Confirm network connectivity
- Review CORS settings

#### 2. File Upload Issues
**Symptoms**: Images don't upload or display
**Solutions**:
- Check file size limits
- Verify file type restrictions
- Confirm BaseRow file storage quota
- Review web server upload limits

#### 3. Session Timeout Issues
**Symptoms**: Users logged out frequently
**Solutions**:
- Adjust session timeout in config.js
- Check browser local storage
- Verify system clock synchronization

#### 4. Performance Issues
**Symptoms**: Slow loading, timeouts
**Solutions**:
- Enable web server compression
- Optimize image sizes
- Implement caching headers
- Consider CDN for static assets

### Debug Mode
Enable debug mode by adding `?debug=true` to any URL:
```
https://your-domain.com/eptw/index.html?debug=true
```

### Log Analysis
Check browser console for JavaScript errors:
1. Open browser developer tools (F12)
2. Navigate to Console tab
3. Look for error messages
4. Check Network tab for failed requests

## Performance Optimization

### 1. Caching Strategy
- **Static Assets**: Cache CSS, JS, images for 1 month
- **API Responses**: Implement client-side caching for reference data
- **Browser Cache**: Configure appropriate cache headers

### 2. Compression
Enable gzip compression on web server:
```apache
# Apache
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
</Location>
```

### 3. Image Optimization
- **Resize**: Automatically resize uploaded images
- **Compression**: Use appropriate JPEG quality settings
- **Format**: Consider WebP format for modern browsers

## Compliance and Auditing

### 1. Audit Trail
- All permit actions are logged with timestamps
- User activities tracked in BaseRow
- Failed login attempts logged

### 2. Data Retention
- Configure BaseRow backup retention
- Implement data archival procedures
- Document compliance requirements

### 3. Access Control
- Regular review of user accounts
- Disable inactive accounts
- Monitor privileged access

## Support and Maintenance

### Contact Information
- **Technical Support**: [Your IT Department]
- **BaseRow Support**: https://baserow.io/support
- **Emergency Contact**: [24/7 Support Number]

### Maintenance Schedule
- **Daily**: Automated health checks
- **Weekly**: Log review and cleanup
- **Monthly**: Security updates and patches
- **Quarterly**: Full system backup and restore test

### Documentation Updates
Keep this deployment guide updated with:
- Configuration changes
- Security updates
- New features
- Troubleshooting solutions
