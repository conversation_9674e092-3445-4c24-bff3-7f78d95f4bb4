const CONFIG = {
    // BaseRow API Configuration
    BASEROW: {
        // Your BaseRow API URL (replace with your actual BaseRow instance URL)
        API_URL: 'https://api.baserow.io/api',
        
        // Your BaseRow API Token (replace with your actual token)
        // Generate this from your BaseRow account settings
        API_TOKEN: 'S2ZLtjxTEWY1NtMJCxUpCH1B1NeBMqwj',
        
        // Database ID (replace with your actual database ID)
        DATABASE_ID: '258637',
        
        // Table IDs (replace with your actual table IDs after creating tables)
        TABLES: {
            PERMITS: '610031',
            AUDITS: '610033',
            USERS: '610029'  // This table ID is causing the 404
        }
    },
    
    // Application Settings
    APP: {
        // Session timeout in milliseconds (24 hours)
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000,
        
        // Auto-refresh interval for dashboards (30 seconds)
        REFRESH_INTERVAL: 30000,
        
        // Maximum file size for image uploads (5MB)
        MAX_FILE_SIZE: 5 * 1024 * 1024,
        
        // Allowed image file types
        ALLOWED_FILE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        
        // Application version
        VERSION: '1.0.0'
    },
    
    // User Roles Configuration
    ROLES: {
        FIREMAN: 'Fireman',
        ELECTRICAL_MANAGER: 'Electrical_Manager',
        MECHANICAL_MANAGER: 'Mechanical_Manager',
        CIVIL_MANAGER: 'Civil_Manager'
    },
    
    // Department Configuration
    DEPARTMENTS: {
        ELECTRICAL: 'Electrical',
        MECHANICAL: 'Mechanical',
        CIVIL: 'Civil'
    },
    
    // Permit Status Configuration
    PERMIT_STATUS: {
        PENDING: 'Pending',
        APPROVED: 'Approved',
        REJECTED: 'Rejected',
        CLOSED: 'Closed'
    },
    
    // Audit Compliance Status
    COMPLIANCE_STATUS: {
        PASS: 'Pass',
        FAIL: 'Fail'
    }
};

// Validation function to check if configuration is properly set
function validateConfig() {
    const requiredFields = [
        'BASEROW.API_TOKEN',
        'BASEROW.DATABASE_ID',
        'BASEROW.TABLES.PERMITS',
        'BASEROW.TABLES.AUDITS',
        'BASEROW.TABLES.USERS'
    ];
    
    const missingFields = [];
    
    requiredFields.forEach(field => {
        const keys = field.split('.');
        let value = CONFIG;
        
        for (const key of keys) {
            value = value[key];
            if (value === undefined || value === null || value.includes('YOUR_')) {
                missingFields.push(field);
                break;
            }
        }
    });
    
    if (missingFields.length > 0) {
        console.warn('ePTW Configuration Warning: The following fields need to be configured:');
        missingFields.forEach(field => console.warn(`- ${field}`));
        return false;
    }
    
    return true;
}

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, validateConfig };
}

